# ServiceClient 使用文档

## 概述

`ServiceClient` 是一个用于调用外部API接口的客户端类，支持本地、测试、生产三种环境，具有完善的错误处理和重试机制。

## 功能特性

- ✅ 支持多环境配置（本地、测试、生产）
- ✅ 自动重试机制
- ✅ 完善的日志记录
- ✅ 错误处理和异常捕获
- ✅ 响应数据解析工具
- ✅ 环境动态切换

## 环境配置

| 环境 | 枚举值 | URL |
|------|--------|-----|
| 本地环境 | `Environment.LOCAL` | `http://127.0.0.1:9051` |
| 测试环境 | `Environment.TEST` | `https://apitest.jinsuosuo.com/core` |
| 生产环境 | `Environment.PRODUCTION` | `https://api.jinsuosuo.com/core` |

## 基本使用

### 1. 导入必要的模块

```python
from client.service_client import ServiceClient, Environment
from common.logger import Logger, INFO
from utils.file_utils import FileUtils
```

### 2. 创建客户端实例

```python
# 创建日志记录器（可选）
logger = Logger(FileUtils.get_project_dir(), 'service_client.log', INFO).log()

# 创建服务客户端
client = ServiceClient(
    environment=Environment.LOCAL,  # 选择环境
    api_token="your-api-token",     # API令牌
    max_retries=3,                  # 最大重试次数
    retry_delay=2,                  # 重试延迟（秒）
    logger_=logger                  # 日志记录器
)
```

### 3. 调用 queryCanUpdate 接口

```python
# 准备请求参数
result = client.query_can_update(
    user_id=9,
    platform="xhs",
    work_url="https://example.com/work",
    submit_time=1749201253000,
    submit_user=[{"unionId": "C9KfMJuvGAysoLhyLXfGuwiEiE"}]
)

# 处理响应
if result:
    if client.is_response_success(result):
        data = client.get_response_data(result)
        print(f"获取到 {len(data)} 条记录")
        for item in data:
            print(f"作品URL: {item['workUrl']}")
            print(f"状态: {item['status']}")
            print(f"过期时间: {item['expireTime']}")
    else:
        print(f"接口调用失败: {client.get_response_message(result)}")
else:
    print("接口调用失败")
```

## API 方法说明

### 核心方法

#### `query_can_update(user_id, platform, work_url, submit_time, submit_user)`

查询是否可以更新的接口

**参数:**
- `user_id` (int): 用户ID
- `platform` (str): 平台标识（如 "xhs"）
- `work_url` (str): 作品URL
- `submit_time` (int): 提交时间戳（毫秒）
- `submit_user` (List[Dict]): 提交用户列表，格式: `[{"unionId": "xxx"}]`

**返回:**
- `dict`: API响应数据，包含 code、result、msg 字段
- `None`: 请求失败

### 工具方法

#### `is_response_success(response)`
检查API响应是否成功

#### `get_response_data(response)`
从API响应中提取结果数据

#### `get_response_message(response)`
从API响应中提取消息

#### `set_environment(environment)`
切换API环境

#### `set_api_token(api_token)`
设置新的API令牌

#### `get_current_environment()`
获取当前环境

#### `get_base_url()`
获取当前基础URL

## 响应数据格式

成功响应示例：
```json
{
    "code": "success",
    "result": [
        {
            "workUrl": "https://www.xiaohongshu.com/explore/xxx",
            "userId": 9,
            "platform": "xhs",
            "submitTime": "2025-06-08 21:38:00",
            "submitUser": ["1d944cca0a8a4e398ab94c6ca9f2308b"],
            "status": 0,
            "expireTime": "2025-07-01 00:00:00",
            "updateTime": "2025-06-08 21:38:06"
        }
    ],
    "msg": "操作成功"
}
```

## 错误处理

客户端内置了完善的错误处理机制：

1. **网络错误**: 自动重试，最多重试3次
2. **HTTP错误**: 记录错误日志并返回None
3. **JSON解析错误**: 记录错误日志并返回None
4. **响应为空**: 记录警告日志并重试

## 最佳实践

1. **使用日志记录器**: 便于调试和监控
2. **检查响应状态**: 使用 `is_response_success()` 检查
3. **处理空响应**: 始终检查返回值是否为None
4. **环境管理**: 根据部署环境选择正确的Environment
5. **令牌安全**: 不要在代码中硬编码API令牌

## 示例代码

完整的使用示例请参考 `test_service_client.py` 文件。
