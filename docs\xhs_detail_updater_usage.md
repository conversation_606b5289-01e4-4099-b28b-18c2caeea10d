# XhsDetailUpdater 使用文档

## 概述

`XhsDetailUpdater` 是一个用于更新小红书作品详情的处理器，它集成了以下功能：

1. 查询可更新的任务列表
2. 将任务记录插入到 `xhs_work_detail_refresh_task` 表
3. 循环处理每个任务，查询作品详情
4. 将详情数据插入到 `xhs_work_detail_refresh_record` 表
5. 标记处理成功的任务

## 功能特性

- ✅ 支持多环境配置（本地、测试、生产）
- ✅ 完整的任务处理流程
- ✅ 详细的日志记录
- ✅ 错误处理和异常捕获
- ✅ 批次处理机制
- ✅ 数据库事务管理

## 数据流程

```
1. 调用 ServiceClient.query_can_update() 
   ↓
2. 生成批次ID，插入任务到 xhs_work_detail_refresh_task 表
   ↓
3. 循环处理每个任务：
   - 调用 Handler.query_article_detail() 查询详情
   - 插入详情到 xhs_work_detail_refresh_record 表
   ↓
4. 调用 ServiceClient.mark_update() 标记成功
```

## 基本使用

### 1. 导入必要的模块

```python
from scene.xhs_detail_updater import XhsDetailUpdater
from client.service_client import Environment
from common.logger import Logger, INFO
from utils.file_utils import FileUtils
```

### 2. 创建更新器实例

```python
# 创建日志记录器
logger = Logger(FileUtils.get_project_dir(), 'xhs_detail_updater.log', INFO).log()

# 创建更新器
updater = XhsDetailUpdater(
    user_id=9,                                              # 用户ID
    api_token="api-token-a8cabb2bf81f04c8284c63db01514d0f",  # API令牌
    logger_=logger,                                         # 日志记录器
    environment=Environment.PRODUCTION                      # API环境
)
```

### 3. 执行更新操作

```python
# 执行更新
result = updater.performing_updates()

if result:
    print("更新操作执行成功!")
else:
    print("更新操作执行失败或无任务需要处理")
```

## API 接口说明

### ServiceClient 接口

#### `query_can_update(user_id, platform)`
查询可更新的任务列表

**请求示例:**
```
GET /api/external/queryCanUpdate?userId=9&platform=xhs
```

**响应示例:**
```json
{
    "code": "success",
    "result": [
        {
            "recordId": 304,
            "rowId": "vZ0gmtMCrO",
            "workUrl": "https://www.xiaohongshu.com/explore/683fe4a700000000200297dc",
            "workId": "683fe4a700000000200297dc",
            "userId": 9,
            "platform": "xhs",
            "status": 0,
            "expireTime": "2025-06-11 00:00:00",
            "nextUpdateTime": "2025-06-10 00:00:00"
        }
    ],
    "msg": "操作成功"
}
```

#### `mark_update(record_id)`
标记任务处理成功

**请求示例:**
```
GET /api/external/markUpdate?recordId=304
```

### Handler 接口

#### `query_article_detail(key)`
查询作品详情

**参数:**
- `key`: 作品URL

**返回:**
- `ArticleDetail` 对象，包含作品的详细信息

## 数据库表结构

### xhs_work_detail_refresh_task 表
存储刷新任务记录

| 字段 | 类型 | 说明 |
|------|------|------|
| id | int | 主键ID |
| company | varchar | 公司标识 |
| work_url | varchar | 作品URL |
| work_id | varchar | 作品ID |
| row_id | varchar | 批次ID |
| status | int | 状态（0-待处理，1-已处理） |
| create_time | datetime | 创建时间 |

### xhs_work_detail_refresh_record 表
存储刷新记录详情

| 字段 | 类型 | 说明 |
|------|------|------|
| id | int | 主键ID |
| row_id | varchar | 批次ID |
| author_id | varchar | 作者ID |
| author_name | varchar | 作者名称 |
| work_id | varchar | 作品ID |
| title | varchar | 作品标题 |
| content | text | 作品内容 |
| read_count | int | 阅读数 |
| like_count | int | 点赞数 |
| comment_count | int | 评论数 |
| share_count | int | 分享数 |
| collect_count | int | 收藏数 |
| ... | ... | 其他字段 |

## 错误处理

更新器内置了完善的错误处理机制：

1. **API调用失败**: 记录错误日志并跳过当前任务
2. **数据库操作失败**: 记录错误日志并继续处理其他任务
3. **详情查询失败**: 记录错误日志并跳过当前任务
4. **网络异常**: 自动重试（由ServiceClient处理）

## 日志记录

系统会记录以下关键信息：

- 任务查询结果
- 批次处理开始/结束
- 每个任务的处理状态
- 详情查询结果
- 数据库操作结果
- 错误和异常信息

## 最佳实践

1. **环境配置**: 根据部署环境选择正确的Environment
2. **日志管理**: 使用合适的日志级别，便于调试和监控
3. **错误处理**: 检查返回值，处理异常情况
4. **性能优化**: 合理设置批次大小，避免过度并发
5. **数据一致性**: 确保数据库操作的原子性

## 示例代码

完整的使用示例请参考 `test_xhs_detail_updater.py` 文件。

## 注意事项

1. 确保数据库连接正常
2. 确保API服务可访问
3. 合理设置日志文件路径
4. 注意API调用频率限制
5. 定期清理过期的任务记录
