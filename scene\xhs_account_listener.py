# -*- coding: utf-8 -*-

import json
import time
from datetime import datetime
from typing import Optional

from tikhub import Client

from client.qb.handler import Handler
from client.qb.models import Platform
from client.tikhub_xhs_client import TikhubXhsClient
from config.xhs_account_config import xhs_dict
from scene.dingtalk_alerter import DingTalkAlerter
from utils.http_utils import HttpUtils
from utils.string_utils import StringUtils


class XhsAccountListener:
    def __init__(self, logger_):
        self.logger_ = logger_
        self.ding_talk_alert = DingTalkAlerter()
        self.client = Client(base_url="https://api.tikhub.io",
                             api_key="6p0MEmst+HkugHAX7XSqoUVvxgIWhozWj6tp4ib4mWFhqGtUdd4v9z+bXw==",
                             max_retries=3,
                             max_connections=50,
                             proxy=None,
                             timeout=60,
                             max_tasks=50)
        self.xhs_client = TikhubXhsClient(logger_=logger_)
        self.qingbo_handler = Handler(logger_=logger_, platform=Platform.XIAOHONGSHU)

    async def sync_new_articles(self):
        for author_id, val in xhs_dict.items():
            await self.query_article_list(author_id, val.get('webhook'))

    async def query_article_list(self, user_id: str, webhook: str):
        articles = []
        use_new_way = False
        notes = await self.get_user_notes(user_id)
        if notes is None:
            notes = await self.get_user_notes(user_id)

        if notes is None:
            use_new_way = True
            # 尝试使用新的接口
            self.logger_.error(f"原始接口获取用户笔记失败，尝试使用新接口user_id :{user_id} webhook :{webhook}")
            notes = await self._get_user_notes_v2(user_id)
            if notes is None:
                self.ding_talk_alert.send(f"获取用户笔记失败：两个接口都请求失败 user_id :{user_id} webhook :{webhook}")
                return

        for note in notes:
            """
            if self._is_timestamp_yesterday(publish_time):
                articles.append(self._parse(note))
            """
            article = self._build_article_adapter(note, use_new_way)

            try:
                publish_time = article.get('publishTime')
                if self._is_within_last_three_days(publish_time):
                    article["needUpdate"] = 1
                else:
                    article["needUpdate"] = 0
            except Exception as e:
                print(e)

            articles.append(article)

        if len(articles) > 0:
            self._notify_webhook(webhook, articles)

    async def get_user_notes(self, user_id: str) -> Optional[list]:
        try:
            cursor = None
            data = await self.client.XiaohongshuWeb.get_user_notes(user_id, cursor)
            if data is None:
                data = await self.client.XiaohongshuWeb.get_user_notes(user_id, cursor)

            data = data.get("data").get("data")
            self.logger_.info(StringUtils.obj_2_json_string(data))

            notes = data.get('notes')
            return notes
        except Exception as e:
            self.logger_.error(e)
            return None

    async def _get_user_notes_v2(self, user_id: str) -> Optional[list]:
        """
            使用新接口获取用户笔记
        """
        try:
            result = self.xhs_client.get_user_notes(user_id=user_id)
            if not result or "data" not in result:
                return None

            notes_data = result.get("data", {}).get("notes", [])
            if not notes_data:
                return None

            item_list = []
            for note in notes_data:
                try:
                    note_id = note.get("note_id")
                    work_url = "https://www.xiaohongshu.com/discovery/item/" + note_id
                    item = self.qingbo_handler.query_article_detail(work_url)
                    if item:
                        item_dict = item.to_dict()
                        item_dict["id"] = note_id
                        item_list.append(item_dict)

                    if len(item_list) > 10:
                        break
                except Exception as e:
                    self.logger_.error(f"处理笔记详情失败: {e}, note_id: {note.get('id')}")
                    continue

                time.sleep(1)

            return item_list

        except Exception as e:
            self.logger_.error(f"_get_user_notes_v2 error: {e}")
            return None

    def _build_article_v2(self, item: dict):
        work_id = item.get('id')
        url = "https://www.xiaohongshu.com/discovery/item/" + work_id

        collect_count = item.get('news_collect_cnt')
        comment_count = item.get('news_comment_count')
        share_count = item.get('news_reposts_count')
        like_count = item.get('news_like_count')

        author_name = item.get('media_name')
        author_id = item.get('media_identity')
        author_avatar = item.get('media_picurl')
        author_url = "https://www.xiaohongshu.com/user/profile/" + author_id

        publish_time = item.get('news_posttime')

        return {
            "platform": "小红书",
            "keyword": "",
            "url": url,
            "title": item.get('news_title'),
            "content": item.get('news_content'),
            "thumbnailLink": item.get("news_headimg_url"),
            "publishTime": publish_time,
            "locationIp": "",
            "authorName": author_name,
            "authorUrl": author_url,
            "likeCount": like_count,
            "commentCount": comment_count,
            "shareCount": share_count,
            "collectCount": collect_count,
            "recordTime": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "needUpdate": 1
        }

    def _build_article(self, note: dict):
        work_id = note.get('id')
        url = "https://www.xiaohongshu.com/discovery/item/" + work_id

        collect_count = note.get('collected_count')
        comment_count = note.get('comments_count')
        share_count = note.get('share_count')
        like_count = note.get('likes')

        user = note.get('user')
        author_name = user.get('nickname')
        author_id = user.get('userid')
        author_avatar = user.get('images')
        author_url = "https://www.xiaohongshu.com/user/profile/" + author_id

        publish_time = note.get('create_time')
        dt_object = datetime.fromtimestamp(publish_time)  # 本地时间
        publish_time_str = dt_object.strftime("%Y-%m-%d %H:%M:%S")

        return {
            "platform": "小红书",
            "keyword": "",
            "url": url,
            "title": note.get('display_title'),
            "content": note.get('desc'),
            "thumbnailLink": "",
            "publishTime": publish_time_str,
            "locationIp": "",
            "authorName": author_name,
            "authorUrl": author_url,
            "likeCount": like_count,
            "commentCount": comment_count,
            "shareCount": share_count,
            "collectCount": collect_count,
            "recordTime": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "needUpdate": 1
        }

    def _build_article_adapter(self, note: dict, use_new_way: bool) -> dict:
        if use_new_way:
            return self._build_article_v2(note)
        else:
            return self._build_article(note)

    def _notify_webhook(self, url, articles):
        data = {"articles": articles}
        json_str = json.dumps(data)

        HttpUtils.post(url, data=json_str)

    def _is_within_last_three_days(self, timestamp):
        now = datetime.now().timestamp()  # 当前时间戳，单位秒
        three_days_ago = now - 3 * 24 * 60 * 60  # 三天前时间戳

        return three_days_ago <= timestamp <= now

    def _is_timestamp_yesterday(self, timestamp):
        """判断时间戳是否属于昨天（本地时区）"""
        # 获取当前时间（本地时区）
        now = datetime.now()

        # 计算今天0点的时间戳
        today_start = datetime.combine(now.date(), time.min).timestamp()

        # 计算昨天0点的时间戳
        yesterday_start = today_start - 24 * 3600

        # 判断时间戳是否在昨天范围内 [昨天0点, 今天0点)
        return yesterday_start <= timestamp < today_start

if __name__ == '__main__':
    pass

    # def _build_article_v2(item: dict):
    #     work_id = item.get('id')
    #     url = "https://www.xiaohongshu.com/discovery/item/" + work_id
    #
    #     collect_count = item.get('news_collect_cnt')
    #     comment_count = item.get('news_comment_count')
    #     share_count = item.get('news_reposts_count')
    #     like_count = item.get('news_like_count')
    #
    #     author_name = item.get('media_name')
    #     author_id = item.get('media_identity')
    #     author_avatar = item.get('media_picurl')
    #     author_url = "https://www.xiaohongshu.com/user/profile/" + author_id
    #
    #     publish_time = item.get('news_posttime')
    #
    #     return {
    #         "platform": "小红书",
    #         "keyword": "",
    #         "url": url,
    #         "title": item.get('news_title'),
    #         "content": item.get('news_content'),
    #         "thumbnailLink": item.get("news_headimg_url"),
    #         "publishTime": publish_time,
    #         "locationIp": "",
    #         "authorName": author_name,
    #         "authorUrl": author_url,
    #         "likeCount": like_count,
    #         "commentCount": comment_count,
    #         "shareCount": share_count,
    #         "collectCount": collect_count,
    #         "recordTime": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
    #         "needUpdate": 1
    #     }
    #
    #
    # itemList = [{'news_uuid': '677b176318d15832789094bf507a5ec3', 'news_url': 'https://www.xiaohongshu.com/discovery/item/67bd917e000000001203de58', 'platform_name': '小红书', 'media_identity': '5cd94250000000001800d40b', 'media_id': '869736611', 'media_name': '陕西大剧院 西安音乐厅', 'news_video_urls': '', 'news_img_urls': 'https://sns-img-hw.xhscdn.com/notes_pre_post/1040g3k031eaq2um2gs005n6p89861l0bnockeq0?imageView2/2/w/900/format/webp;https://sns-img-hw.xhscdn.com/notes_pre_post/1040g3k031eaq2um2gs0g5n6p89861l0b5ailpt8?imageView2/2/w/900/format/webp;https://sns-img-hw.xhscdn.com/notes_pre_post/1040g3k031eaq2um2gs105n6p89861l0bcndf6jo?imageView2/2/w/900/format/webp;https://sns-img-hw.xhscdn.com/notes_pre_post/1040g3k031eaq2um2gs1g5n6p89861l0bs9ul8p8?imageView2/2/w/900/format/webp', 'news_title': '刷到赚到！在🍠买票\u200d 🔥送小音“全家桶”', 'news_digest': '刷到赚到！在🍠买票\u200d 🔥送小音“全家桶”', 'news_posttime': '2025-02-25 17:46:38', 'news_content_ip_location': '', 'news_content': '\n  家人们！！！😱 历时N个月（奶茶续命版）💻 终于把【小红书购票功能】搞！定！了！🎫 陕西大剧院西安音乐厅开元大剧院演出票统统能买！ 刷完repo直接下单拔草的快乐，谁懂啊！ 💥首发宠粉活动💥 第1、11、21、31、41、51、61、71、81、91位 通过小红书小程序下单的用户 送【小音全家桶大礼包】👇 ⚠️超级星期三福袋票、演出公益票不参与活动 ✅ 限定小音毛绒玩偶 ✅ 观演手机背夹 ✅ 小音票夹-捏捏本 ✅ 小音身份证保护套 ✅小音编织手拎袋 买张票就能白嫖周边，这波血赚！ 🔥怎么买？2步拿下！ 1⃣ 陕西大剧院西安音乐厅小红书账号首页「点击商品」 2⃣ 选你想看的演出下单即可 P.S.折扣、积分、权益等均与 🌏小程序同享 ⚡️手快的朋友连票带周边一起薅走吧！ 📣活动仅限通过小红书小程序购票观众参与哦（以付款时间为准） 赶紧@ 你的追剧搭子来抢首发！💨 #小红书[话题]# #小程序[话题]# #福利[话题]# #送周边[话题]# #陕西大剧院[话题]# \n', 'news_read_count': 0, 'news_like_count': 47, 'news_comment_count': 64, 'news_collect_cnt': 6, 'news_reposts_count': 37, 'news_headimg_url': 'https://sns-img-hw.xhscdn.com/notes_pre_post/1040g3k031eaq2um2gs005n6p89861l0bnockeq0?imageView2/2/w/900/format/webp', 'media_picurl': 'https://sns-avatar-qc.xhscdn.com/avatar/60dec21fa68fb20001ce316d.jpg?imageView2/2/w/120/format/jpg', 'music_url': '', 'music_author_name': '', 'music_id': '', 'music_name': '', 'id': '67bd917e000000001203de58'}, {'news_uuid': '94d89370c76613ad8528a911547a0ff0', 'news_url': 'https://www.xiaohongshu.com/discovery/item/60e6c992000000002103472d', 'platform_name': '小红书', 'media_identity': '5cd94250000000001800d40b', 'media_id': '869736611', 'media_name': '陕西大剧院 西安音乐厅', 'news_video_urls': 'http://sns-video-bd.xhscdn.com/744c3ee110e40643006b31afa86e10fa9155c04b_r_ln?v=2', 'news_img_urls': 'https://sns-img-hw.xhscdn.com/e67bf02d-3724-3202-986e-17292846427a?imageView2/2/w/900/format/webp', 'news_title': '被女神倪妮实名推荐的打卡地👠', 'news_digest': '被女神倪妮实名推荐的打卡地👠', 'news_posttime': '2021-07-08 17:46:58', 'news_content_ip_location': '', 'news_content': '\n  前阵子《幺幺洞捌》来西安巡演 就是在我们陕西大剧院没错啦！☺ 演后谈的环节倪妮️表示非常喜欢我们🥰 可以说是从硬件到软件 从头到尾夸了一遍 虽然还是有点不好意思啦 但还是欣然接受了哈哈哈 你们有没有兴趣打卡女神同款大剧院呀？ →来自一个很想红的剧院🎭 西安旅游攻略 | 西安探店 | 西安网红打卡地 | 西安拍照好去处 | 西安周末去哪儿 | 剧院 | 演出 | 舞蹈 | 话剧 #倪妮[话题]# \n', 'news_read_count': 0, 'news_like_count': 748, 'news_comment_count': 65, 'news_collect_cnt': 131, 'news_reposts_count': 54, 'news_headimg_url': 'https://sns-img-hw.xhscdn.com/e67bf02d-3724-3202-986e-17292846427a?imageView2/2/w/900/format/webp', 'media_picurl': 'https://sns-avatar-qc.xhscdn.com/avatar/60dec21fa68fb20001ce316d.jpg?imageView2/2/w/120/format/jpg', 'music_url': '', 'music_author_name': '', 'music_id': '', 'music_name': '', 'id': '60e6c992000000002103472d'}]
    # for item in itemList:
    #     jsons = _build_article_v2(item)
    #     print(jsons)