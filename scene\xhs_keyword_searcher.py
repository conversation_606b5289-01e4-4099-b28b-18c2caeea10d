# -*- coding: utf-8 -*-
import time
from datetime import datetime
from typing import Optional

from dateutil import parser
from tikhub import Client

from client.dingtalk_linker import DingTalkLinker
from client.qb.handler import Handler
from client.qb.models import Platform
from client.tikhub_xhs_client import TikhubXhsClient
from dao.model.author_work import AuthorWork
from dao.repository.at_xhs_author_work_record_repository import AtXhsAuthorWorkRecordRepository
from scene.data_sync import DataSync
from scene.dingtalk_alerter import DingTalkAlerter
from utils.string_utils import StringUtils
from utils.time_utils import TimeUtils


class XhsKeywordSearcher:
    def __init__(self, logger_):
        self.logger_ = logger_

        self.dingtalk_linker = DingTalkLinker(logger_)
        self.ding_talk_alert = DingTalkAlerter()
        self.xhs_work_repo = AtXhsAuthorWorkRecordRepository()

        self.client = Client(base_url="https://api.tikhub.io",
                             api_key="6p0MEmst+HkugHAX7XSqoUVvxgIWhozWj6tp4ib4mWFhqGtUdd4v9z+bXw==",
                             max_retries=3,
                             max_connections=50,
                             timeout=60,
                             max_tasks=50)
        self.xhs_client = TikhubXhsClient(logger_=logger_)
        self.qingbo_handler = Handler(logger_=logger_, platform=Platform.XIAOHONGSHU)

    async def search_key_word(self, keyword, count, data_sync: DataSync):
        article_map = {}
        notify_list = []

        page = 1
        total_count = 0
        while total_count <= count:
            try:
                use_new_way = False
                is_success, has_more, items = await self._query_keyword_page(keyword, page)

                # 当原始搜索无结果时，尝试使用新的 API
                if not is_success:
                    use_new_way = True
                    self.logger_.info(f"原始搜索无结果，尝试使用新的 V2 API 搜索关键词: {keyword}")
                    is_success, has_more, items = await self._query_keyword_page_v2(keyword, page)

                total_count += len(items) if items else 0

                for item in items:
                    author_work: AuthorWork = self._build_article_adapter(item, use_new_way)
                    if author_work is None:
                        continue

                    if article_map.get(author_work.work_id) is not None:
                        continue

                    article_map[author_work.work_id] = author_work
                    notify_list.append(author_work)

                    try:
                        # 同步数据库
                        work_entity = self.xhs_work_repo.query_work_detail(work_id=author_work.work_id)
                        if len(work_entity) > 0:
                            self.xhs_work_repo.update_work_detail(author_work.content, 0,
                                                                  author_work.like_count,
                                                                  author_work.comment_count,
                                                                  author_work.share_count,
                                                                  author_work.collect_count,
                                                                  author_work.work_id)
                        else:
                            self.xhs_work_repo.insert(author_work)
                    except Exception as e:
                        self.logger_.error(f"search_key_word database error: {e}")

                # 同步数据与多维表
                data_sync.invoke(keyword, notify_list)
                notify_list.clear()

                if not has_more or len(items) == 0:
                    break

                page += 1
            except Exception as e:
                self.logger_.error(f"client.bitable.v1.app_table_record.create failed, error: {e}")

    async def _query_keyword_page(self, keyword,
                                  page: int = 1,
                                  sort: str = "general",
                                  noteType: str = "_0"):
        try:
            # return False, False, []
            data = await self.client.XiaohongshuWeb.search_notes(keyword, page, sort, noteType)
            if data is None:
                data = await self.client.XiaohongshuWeb.search_notes(keyword, page, sort, noteType)

            self.logger_.info(StringUtils.obj_2_json_string(data))
            if data is None:
                return False, True, None

            code = data.get("code")
            if code != 200:
                return False, True, None

            data = data.get("data").get("data")
            items = data.get('items')

            return True, len(items) > 0, items
        except Exception as e:
            self.logger_.error("{e}")
            return False, True, None

    async def _query_keyword_page_v2(self,
                                     keyword: str,
                                     page: int = 1,
                                     sort: str = "general",
                                     noteType: str = "0"):
        """
            新版搜索接口，包含详情获取
        """

        try:
            search_result = self.xhs_client.get_search_notes(keyword=keyword, page=page, sort=sort, noteType=noteType)
            if search_result is None:
                search_result = self.xhs_client.get_search_notes(keyword=keyword, page=page, sort=sort,
                                                                 noteType=noteType)

            self.logger_.info(StringUtils.obj_2_json_string(search_result))

            if not search_result or "data" not in search_result:
                return False, False, None
            # 无最新记录
            notes = search_result.get("data", {}).get("items", [])
            if not notes:
                return True, False, []

            code = search_result.get("code")
            if code != 200:
                return False, True, None

            items = []
            for note in notes:
                try:
                    work_url = "https://www.xiaohongshu.com/discovery/item/" + note.get("id")
                    item = self.qingbo_handler.query_article_detail(key=work_url)
                    if item:
                        item_dict = item.to_dict()
                        item_dict["id"] = note.get("id")
                        items.append(item_dict)
                    time.sleep(1)
                except Exception as e:
                    self.logger_.error(f"获取笔记详情失败: {e}, note_id: {note.get('id')}")
                    continue

            return True, len(items) > 0, items

        except Exception as e:
            self.logger_.error(f"_query_keyword_page_v2 error: {e}")
            return False, False, None

    async def search_notes(self, keyword, page, sort, noteType):
        try:
            data = await self.client.XiaohongshuWeb.search_notes(keyword, page, sort, noteType)
            if data is None:
                data = await self.client.XiaohongshuWeb.search_notes(keyword, page, sort, noteType)

            self.logger_.info(StringUtils.obj_2_json_string(data))

            data = data.get("data").get("data")
            notes = data.get('notes')
            return notes
        except Exception as e:
            self.logger_.error(e)
            return None

    def _get_thumbnailLink(self, note):
        try:
            images_list = note.get('images_list')
            if len(images_list) > 0:
                image = images_list[0]
                return image.get('url')
        except Exception as e:
            return ''

    def _build_article_adapter(self, item: dict, use_new_way: bool) -> Optional[AuthorWork]:
        if use_new_way:
            return self._build_article_v2(item)
        else:
            return self._build_article(item)

    def _build_article(self, item: dict) -> Optional[AuthorWork]:
        try:
            note = item.get('note')

            user = note.get('user')
            author_name = user.get('nickname')
            author_id = user.get('userid')
            author_avatar = user.get('images')
            author_url = "https://www.xiaohongshu.com/user/profile/" + author_id

            work_id = note.get('id')
            url = "https://www.xiaohongshu.com/discovery/item/" + work_id
            title = note.get('title')
            content = note.get('desc')
            collect_count = note.get('collected_count')
            comment_count = note.get('comments_count')
            share_count = note.get('shared_count')
            like_count = note.get('liked_count')
            publish_time = note.get('timestamp')
            dt_object = datetime.fromtimestamp(publish_time)  # 本地时间
            publish_time_str = dt_object.strftime("%Y-%m-%d %H:%M:%S")
            publish_day = dt_object.strftime("%Y-%m-%d")

            return AuthorWork(_id=0, platform='xhs', author_id=author_id,
                              author_identity=author_id, author_avatar=author_avatar,
                              author_name=author_name, author_url=author_url,
                              work_id=work_id, work_uuid=work_id, url=url,
                              download_url='', long_url=url, digest=content,
                              title=title, thumbnail_link=self._get_thumbnailLink(note),
                              content=content, img_urls='', video_urls='', music_url='', music_author_name='',
                              music_id='', music_name='', publish_time=publish_time_str,
                              publish_day=publish_day, location_ip='', read_count=0,
                              like_count=like_count, comment_count=comment_count,
                              share_count=share_count, collect_count=collect_count,
                              text_polarity=-1,
                              record_time=TimeUtils.get_current_ts(),
                              is_detail=1
                              )
        except Exception as e:
            self.logger_.error(f"{StringUtils.obj_2_json_string(item)}, {e}")
            return None

    def _build_article_v2(self, item: dict) -> Optional[AuthorWork]:
        try:
            author_name = item.get('media_name', '')
            author_id = item.get('media_identity', '')
            author_avatar = item.get('media_picurl', '')
            author_url = "https://www.xiaohongshu.com/user/profile/" + author_id

            work_id = item.get('id', '')
            url = "https://www.xiaohongshu.com/discovery/item/" + work_id
            title = item.get('news_title', '')
            content = item.get('news_content')
            collect_count = item.get('news_collect_cnt')
            comment_count = item.get('news_comment_count')
            share_count = item.get('news_reposts_count')
            like_count = item.get('news_like_count')
            publish_time = item.get('news_posttime')

            date_obj = parser.parse(publish_time)
            publish_day = date_obj.strftime("%Y-%m-%d")
            thumbnail_link = item.get('news_headimg_url', '')

            return AuthorWork(_id=0, platform='xhs', author_id=author_id,
                              author_identity=author_id, author_avatar=author_avatar,
                              author_name=author_name, author_url=author_url,
                              work_id=work_id, work_uuid=work_id, url=url,
                              download_url='', long_url=url, digest=content,
                              title=title, thumbnail_link=thumbnail_link,
                              content=content, img_urls='', video_urls='', music_url='', music_author_name='',
                              music_id='', music_name='', publish_time=publish_time,
                              publish_day=publish_day, location_ip='', read_count=0,
                              like_count=like_count, comment_count=comment_count,
                              share_count=share_count, collect_count=collect_count,
                              text_polarity=-1,
                              record_time=TimeUtils.get_current_ts(),
                              is_detail=1
                              )
        except Exception as e:
            self.logger_.error(f"{StringUtils.obj_2_json_string(item)}, {e}")
            return None


if __name__ == '__main__':
    pass

    # itemList = [{'news_uuid': '677b176318d15832789094bf507a5ec3', 'news_url': 'https://www.xiaohongshu.com/discovery/item/67bd917e000000001203de58', 'platform_name': '小红书', 'media_identity': '5cd94250000000001800d40b', 'media_id': '869736611', 'media_name': '陕西大剧院 西安音乐厅', 'news_video_urls': '', 'news_img_urls': 'https://sns-img-hw.xhscdn.com/notes_pre_post/1040g3k031eaq2um2gs005n6p89861l0bnockeq0?imageView2/2/w/900/format/webp;https://sns-img-hw.xhscdn.com/notes_pre_post/1040g3k031eaq2um2gs0g5n6p89861l0b5ailpt8?imageView2/2/w/900/format/webp;https://sns-img-hw.xhscdn.com/notes_pre_post/1040g3k031eaq2um2gs105n6p89861l0bcndf6jo?imageView2/2/w/900/format/webp;https://sns-img-hw.xhscdn.com/notes_pre_post/1040g3k031eaq2um2gs1g5n6p89861l0bs9ul8p8?imageView2/2/w/900/format/webp', 'news_title': '刷到赚到！在🍠买票\u200d 🔥送小音“全家桶”', 'news_digest': '刷到赚到！在🍠买票\u200d 🔥送小音“全家桶”', 'news_posttime': '2025-02-25 17:46:38', 'news_content_ip_location': '', 'news_content': '\n  家人们！！！😱 历时N个月（奶茶续命版）💻 终于把【小红书购票功能】搞！定！了！🎫 陕西大剧院西安音乐厅开元大剧院演出票统统能买！ 刷完repo直接下单拔草的快乐，谁懂啊！ 💥首发宠粉活动💥 第1、11、21、31、41、51、61、71、81、91位 通过小红书小程序下单的用户 送【小音全家桶大礼包】👇 ⚠️超级星期三福袋票、演出公益票不参与活动 ✅ 限定小音毛绒玩偶 ✅ 观演手机背夹 ✅ 小音票夹-捏捏本 ✅ 小音身份证保护套 ✅小音编织手拎袋 买张票就能白嫖周边，这波血赚！ 🔥怎么买？2步拿下！ 1⃣ 陕西大剧院西安音乐厅小红书账号首页「点击商品」 2⃣ 选你想看的演出下单即可 P.S.折扣、积分、权益等均与 🌏小程序同享 ⚡️手快的朋友连票带周边一起薅走吧！ 📣活动仅限通过小红书小程序购票观众参与哦（以付款时间为准） 赶紧@ 你的追剧搭子来抢首发！💨 #小红书[话题]# #小程序[话题]# #福利[话题]# #送周边[话题]# #陕西大剧院[话题]# \n', 'news_read_count': 0, 'news_like_count': 47, 'news_comment_count': 64, 'news_collect_cnt': 6, 'news_reposts_count': 37, 'news_headimg_url': 'https://sns-img-hw.xhscdn.com/notes_pre_post/1040g3k031eaq2um2gs005n6p89861l0bnockeq0?imageView2/2/w/900/format/webp', 'media_picurl': 'https://sns-avatar-qc.xhscdn.com/avatar/60dec21fa68fb20001ce316d.jpg?imageView2/2/w/120/format/jpg', 'music_url': '', 'music_author_name': '', 'music_id': '', 'music_name': '', 'id': '67bd917e000000001203de58'}, {'news_uuid': '94d89370c76613ad8528a911547a0ff0', 'news_url': 'https://www.xiaohongshu.com/discovery/item/60e6c992000000002103472d', 'platform_name': '小红书', 'media_identity': '5cd94250000000001800d40b', 'media_id': '869736611', 'media_name': '陕西大剧院 西安音乐厅', 'news_video_urls': 'http://sns-video-bd.xhscdn.com/744c3ee110e40643006b31afa86e10fa9155c04b_r_ln?v=2', 'news_img_urls': 'https://sns-img-hw.xhscdn.com/e67bf02d-3724-3202-986e-17292846427a?imageView2/2/w/900/format/webp', 'news_title': '被女神倪妮实名推荐的打卡地👠', 'news_digest': '被女神倪妮实名推荐的打卡地👠', 'news_posttime': '2021-07-08 17:46:58', 'news_content_ip_location': '', 'news_content': '\n  前阵子《幺幺洞捌》来西安巡演 就是在我们陕西大剧院没错啦！☺ 演后谈的环节倪妮️表示非常喜欢我们🥰 可以说是从硬件到软件 从头到尾夸了一遍 虽然还是有点不好意思啦 但还是欣然接受了哈哈哈 你们有没有兴趣打卡女神同款大剧院呀？ →来自一个很想红的剧院🎭 西安旅游攻略 | 西安探店 | 西安网红打卡地 | 西安拍照好去处 | 西安周末去哪儿 | 剧院 | 演出 | 舞蹈 | 话剧 #倪妮[话题]# \n', 'news_read_count': 0, 'news_like_count': 748, 'news_comment_count': 65, 'news_collect_cnt': 131, 'news_reposts_count': 54, 'news_headimg_url': 'https://sns-img-hw.xhscdn.com/e67bf02d-3724-3202-986e-17292846427a?imageView2/2/w/900/format/webp', 'media_picurl': 'https://sns-avatar-qc.xhscdn.com/avatar/60dec21fa68fb20001ce316d.jpg?imageView2/2/w/120/format/jpg', 'music_url': '', 'music_author_name': '', 'music_id': '', 'music_name': '', 'id': '60e6c992000000002103472d'}]
    # for item in itemList:
    #     jsons = _build_article_v2(item)
    #     print(jsons)
