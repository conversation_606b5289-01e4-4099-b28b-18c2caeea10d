# -*- coding: utf-8 -*-

import json
import os
import sys
import asyncio

from common.env import Env
from scene.data_sync import DingTalkDataSync, LarkBitableDataSync
from scene.xhs_account_listener import XhsAccountListener
from scene.xhs_keyword_searcher import XhsKeywordSearcher

cur_path = os.path.dirname(os.path.abspath(__file__))
project_path = os.path.split(cur_path)[0]
sys.path.append(cur_path)
sys.path.append(project_path)


logger_ = Env().get_main_logger()

def dingtalk_bitable_linker_test():
    keyword = '话剧 父亲 金士杰'
    webhook = "https://connector.dingtalk.com/webhook/flow/bca99474910679c93dcd82cb"
    dentry_uuid = "YQBnd5ExVE1ydrXxHMz125KMVyeZqMmz"
    id_or_name = "作品数据-自定义"
    count = 200

    data_sync = DingTalkDataSync(logger_, webhook, dentry_uuid, id_or_name)

    xhs_keyword_searcher = XhsKeywordSearcher(logger_)
    asyncio.run(xhs_keyword_searcher.search_key_word(keyword, count, data_sync))

def lark_bitable_test():
    app_id = "cli_a72e919930b2100d"
    app_secret = "SfM4aazxDOMIwRbkrlk04fIKampAAUHt"
    app_token = "ZNWqbeUmoav5n9s9P6pcKeqPn8f"
    article_table_id = "tbl60AQVX0jas4n1"

    data_sync = LarkBitableDataSync(logger_, app_id, app_secret, app_token, article_table_id)

    # "东盛烤肉", "东盛新品", "东盛自助餐"
    keyword = '东盛自助餐'
    count = 200

    xhs_keyword_searcher = XhsKeywordSearcher(logger_)
    asyncio.run(xhs_keyword_searcher.search_key_word(keyword, count, data_sync))


if __name__ == "__main__":
    lark_bitable_test()
    # lark_bitable_test()
