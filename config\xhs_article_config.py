# -*- coding: utf-8 -*-

updater_dict = {
    "佳尔优优": ["task-update-article-001"],
    "朱栈科技": ["task-update-article-002"],
}

article_task_dict = {
    "task-update-article-001": {
        "user_info": {
            "api-token": "api-token-xxx",
            "user_id": 9
        },
        "platform": ["xhs"],
        "cron_desc": "每周五 上午10点, 每月底上午10点",
        "handler": "dingtalk-bitable-linker",
        "bitable": {
            "webhook": "https://connector.dingtalk.com/webhook/flow/1bff53039ddc25b4ffa87107",
            "dentryUuid": "7QG4Yx2JpL5GMpbQUYK32bQYJ9dEq3XD",
            "idOrName": "作品数据"
        },
        "is_active": False
    },

    "task-update-article-002": {
        "company": "朱栈科技",
        "user_info": {
            "api-token": "api-token-71726a26c98b6a63a9bc9cac43ecab13",
            "user_id": 121
        },
        "platform": ["dy"],
        "cron_desc": "发布后第二天、发布后第 7 天",
        "handler": "dingtalk-bitable-linker",
        "bitable": {
            "webhook": "https://connector.dingtalk.com/webhook/flow/103224188fe70bf8e42e000o"
        },
        "is_active": True
    }
}
