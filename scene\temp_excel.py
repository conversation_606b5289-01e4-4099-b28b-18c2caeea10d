# -*- coding: utf-8 -*-
import time
import pandas as pd
import requests
from client.qb.handler import Handler
from client.qb.models import ArticleDetail
from common.env import Env

logger_ = Env().get_main_logger()


def process_xhs_excel():
    """
    处理红书Excel数据并调用更新接口
    """
    # 读取Excel
    df = pd.read_excel(r'F:\py\xhs_project\scene\recent_week.xlsx')

    # API配置
    api_url = 'https://api.jinsuosuo.com/core/api/external/workUpdate'
    headers = {
        'Authorization': 'api-token-71726a26c98b6a63a9bc9cac43ecab13',
        'Content-Type': 'application/json'
    }

    # 处理每一行数据
    for index, row in df.iterrows():
        try:
            platform = "xhs"
            if row["平台"] == "抖音":
                platform = "dy"

            data = {
                "workUrl": row['发布链接'],
                "publishTime": pd.to_datetime(row['发布时间']).strftime('%Y-%m-%d %H:%M'),
                "platform": platform,
                "rowId": row['记录ID']
            }

            # 调用API
            response = requests.post(api_url, json=data, headers=headers)

            if response.status_code == 200:
                logger_.info(f"Successfully updated record {row['记录ID']}")
            else:
                logger_.error(f"Failed to update record {row['记录ID']}: {response.status_code}")

            # 避免请求过快
            time.sleep(1)

        except Exception as e:
            logger_.error(f"Error processing record {row['记录ID']}: {str(e)}")
            continue


def process_excel():
    # 初始化Handler
    handler = Handler(logger_, "xhs")

    # 读取Excel
    df = pd.read_excel(r'C:\Users\<USER>\Downloads\数据-抖音.xlsx')

    # 存储API请求结果
    results = []

    # 处理每一行数据
    for index, row in df.iterrows():
        # 从链接中提取key
        url = row['链接']
        print(f"start url --- {url}")

        # 调用API查询文章详情
        detail: ArticleDetail = handler.query_article_detail(key=url)

        if detail:
            result = {
                'rowId': row['行记录ID'],
                'workUrl': url,
                'likeCount': detail.news_like_count,
                'commentCount': detail.news_comment_count,
                'shareCount': detail.news_reposts_count,
                'collectCount': detail.news_collect_cnt
            }
            results.append(result)

            print(f"Processed {index + 1}/{len(df)} records")
            logger_.info(f"Processed {index + 1}/{len(df)} records")

        # 等待2秒
        time.sleep(2)

    # 将结果保存回Excel
    result_df = pd.DataFrame(results)
    result_df.to_excel('result.xlsx', index=False)

    # 发送钉钉请求
    webhook_url = 'https://connector.dingtalk.com/webhook/flow/103224188fe70bf8e42e000o'
    headers = {'Content-Type': 'application/json'}
    payload = {'data': results}

    response = requests.post(webhook_url, json=payload, headers=headers)
    if response.status_code == 200:
        logger_.info("Successfully sent data to DingTalk")
    else:
        logger_.error(f"Failed to send data to DingTalk: {response.status_code}")


def retry_webhook(webhook_url: str, max_retries=3, retry_delay=5):
    """
    从result.xlsx读取数据并重试发送到钉钉webhook
    
    Args:
        webhook_url: 钉钉webhook URL
        max_retries: 最大重试次数
        retry_delay: 重试间隔(秒)
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(r'C:\Users\<USER>\Desktop\xhs-result.xlsx')

        # 转换数据为所需格式
        data = []
        for _, row in df.iterrows():
            item = {
                'rowId': row['rowId'],
                'workUrl': row['workUrl'],
                'likeCount': int(row['likeCount']),
                'commentCount': int(row['commentCount']),
                'shareCount': int(row['shareCount']),
                'collectCount': int(row['collectCount'])
            }
            data.append(item)

        # 准备请求
        headers = {'Content-Type': 'application/json'}
        payload = {'data': data}

        # 重试逻辑
        for attempt in range(max_retries):
            try:
                response = requests.post(webhook_url, json=payload, headers=headers)
                if response.status_code == 200:
                    logger_.info("Successfully sent data to DingTalk on attempt %d", attempt + 1)
                    return True
                else:
                    logger_.warning("Failed to send data to DingTalk on attempt %d: %s",
                                    attempt + 1, response.status_code)
            except Exception as e:
                logger_.error("Error sending data to DingTalk on attempt %d: %s",
                              attempt + 1, str(e))

            if attempt < max_retries - 1:
                logger_.info("Retrying in %d seconds...", retry_delay)
                time.sleep(retry_delay)

        logger_.error("Failed to send data to DingTalk after %d attempts", max_retries)
        return False

    except Exception as e:
        logger_.error("Error reading result.xlsx: %s", str(e))
        return False


if __name__ == '__main__':
    process_xhs_excel()  # 调用新的处理函数
