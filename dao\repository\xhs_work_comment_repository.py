# -*- coding: utf-8 -*-

import datetime

from client.qb.models import Comment
from dao.db_adapter import g_database_product
from dao.model.work_comment import WorkComment


class XhsWorkCommentRepository(object):
    """
    对应表格 bardata 操作
    """

    def __init__(self):
        self.gdb_datasource = g_database_product

    def insert(self, author_id, work_id, work_url, platform, uuid, work_comment: Comment):
        sql = '''
            INSERT INTO `at_xhs_work_comment_record`(
                `author_id`, `work_id`, `work_url`, `comment_uuid`, 
                `commenter_name`, `commenter_url`, `commenter_id`, `content`, `publish_time`,
                 `like_count`, `location_ip`, `text_polarity`, `record_time`, `is_del`) 
            VALUES (%s,%s,%s, %s,%s,%s, %s,%s,%s, %s,%s,%s, %s,%s);'''

        commenter_url = "https://www.xiaohongshu.com/user/profile/" + work_comment.comment_user_id
        record_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        args = (author_id, work_id, work_url, uuid, work_comment.comment_user,
                work_comment.news_url, work_comment.comment_user_id, work_comment.comment_content,
                work_comment.comment_posttime, work_comment.comment_like_count,
                work_comment.comment_ip_location,
                -2, record_time, 0)

        return self.gdb_datasource.insert(sql=sql, args=args)

    def query_comment_info(self, comment_uuid):
        sql = 'select * from at_xhs_work_comment_record where comment_uuid=%s'
        return self.gdb_datasource.fetch_all(sql, (comment_uuid))

    def query_comment_list(self, work_id):
        sql = 'select * from at_xhs_work_comment_record where work_id=%s'
        return self.gdb_datasource.fetch_all(sql, (work_id))


    def update_comment_content(self, content, url, publish_time, commenter_id):
        sql = 'update at_xhs_work_comment_record set content=%s ' \
              'where commenter_url=%s and publish_time=%s and commenter_id=%s'
        return self.gdb_datasource.execute(sql, (content, url, publish_time, commenter_id))

    @staticmethod
    def build_author_work_comment_entity(_row, author_name, title):
        return WorkComment(
            _id=_row['id'],
            author_id=_row['author_id'],
            author_name=author_name,
            work_id=_row['work_id'],
            work_title=title,
            work_url=_row['work_url'],
            platform="小红书",
            comment_uuid=_row['comment_uuid'],
            commenter_name=_row['commenter_name'],
            commenter_id=_row['commenter_id'],
            commenter_url=_row['commenter_url'],
            content=_row['content'],
            publish_time=_row['publish_time'],
            like_count=_row['like_count'],
            location_ip=_row['location_ip'],
            text_polarity=_row['text_polarity'],
            record_time=_row['record_time'],
        )
