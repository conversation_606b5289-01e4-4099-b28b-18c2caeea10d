# -*- coding: utf-8 -*-

import time
import base64
import hashlib

from client.dingtalk_linker import DingTalk<PERSON>inker
from client.qb.handler import Handler
from client.qb.models import Platform
from dao.repository.xhs_keyword_article_record_repository import XhsKeywordArticleRecordRepository
from dao.repository.xhs_keyword_status_record_repository import XhsKeywordStatusRecordRepository
from dao.repository.xhs_work_comment_repository import XhsWorkCommentRepository
from dao.repository.xhs_work_detail_record_repository import XhsWorkDetailRecordRepository
from scene.dingtalk_alerter import DingTalkAlerter


class XhsKeywordWorkCommentRequester:
    def __init__(self, logger_):
        self.logger_ = logger_

        self.ding_talk_alert = DingTalkAlerter()
        self.dingtalk_linker = DingTalkLinker(logger_)
        self.xhs_work_repo = XhsWorkDetailRecordRepository()
        self.xhs_keyword_status = XhsKeywordStatusRecordRepository()
        self.xhs_keyword_article = XhsKeywordArticleRecordRepository()
        self.xhs_work_comment_rep = XhsWorkCommentRepository()
        self.qingbo_handler = Handler(logger_=logger_, platform=Platform.XIAOHONGSHU)

    def update_work_comment(self, record_id):
        rows = self.xhs_keyword_article.query_work_detail_list(record_id)
        for row in rows:
            try:
                work_id = row['work_id']
                work_record_list = self.xhs_keyword_article.query_work_record(record_id, work_id)
                if len(work_record_list) == 0:
                    continue

                work_record = work_record_list[0]
                if work_record['comment_status'] == 1:
                    continue

                nwork_list = self.xhs_work_repo.query_work_detail(work_id)
                if len(nwork_list) == 0:
                    continue

                nwork = nwork_list[0]

                comment_count = nwork['comment_count']
                author_id = nwork['author_id']
                if comment_count == 0:
                    self.xhs_keyword_article.update_work_comment_status(record_id, work_id, 1)
                    time.sleep(1)
                    continue

                comment_list = self.query_work_comment_by_qingbo(work_id)
                if comment_list is None:
                    continue

                for comment in comment_list:
                    try:
                        uuid = self._hash_comment(comment.comment_user_id,
                                                  comment.comment_content,
                                                  comment.comment_posttime)

                        self.xhs_work_comment_rep.insert(author_id,
                                                         work_id,
                                                         "https://www.xiaohongshu.com/discovery/item/" + work_id,
                                                         "xhs",
                                                         uuid,
                                                         comment)
                    except Exception as e:
                        print(e)

                self.xhs_keyword_article.update_work_comment_status(record_id, work_id, 1)
                time.sleep(1)
            except Exception as e:
                self.logger_.error("update_work_detail error={e}")

    def query_work_comment_by_qingbo(self, work_id, count=5):
        try:
            work_url = "https://www.xiaohongshu.com/discovery/item/" + work_id
            items = self.qingbo_handler.query_article_comment(key=work_url, count=count)
            if items is None:
                time.sleep(1)
                items = self.qingbo_handler.query_article_comment(key=work_url, count=count)

            if items is None:
                time.sleep(1)
                items = self.qingbo_handler.query_article_comment(key=work_url, count=count)

            return items
        except Exception as e:
            self.logger_.error(f"获取笔记详情失败: {e}, note_id: {work_id}")
            return None

    def _get_thumbnailLink(self, note):
        try:
            images_list = note.get('images_list')
            if len(images_list) > 0:
                image = images_list[0]
                return image.get('url')
        except Exception as e:
            return ''

    def _insert_keyword_work_record(self, keyword, record_id, work_id):
        self.xhs_keyword_article.insert(keyword, record_id, work_id)

    def _insert_keyword_status_record(self, keyword, record_id, status):
        self.xhs_keyword_status.insert(keyword, record_id, status)

    def _update_keyword_status_record(self, record_id, status):
        self.xhs_keyword_status.update(record_id, status)

    def _hash_comment(self, comment_user, comment_content, comment_time):
        try:
            input_str = f"{comment_user}:{comment_content}:{comment_time}"
            hash_obj = hashlib.sha256(input_str.encode('utf-8'))
            hash_bytes = hash_obj.digest()
            return base64.b64encode(hash_bytes).decode('utf-8')
        except Exception:
            return None

    def insert_comment(self, work_id):
        works = self.xhs_work_repo.query_work_detail(work_id)
        work = works[0]
        author_id = work['author_id']

        comment_list = self.query_work_comment_by_qingbo(work_id)
        if comment_list is None:
            return None

        for comment in comment_list:
            try:
                uuid = self._hash_comment(comment.comment_user_id,
                                          comment.comment_content,
                                          comment.comment_posttime)

                self.xhs_work_comment_rep.insert(author_id,
                                                 work_id,
                                                 "https://www.xiaohongshu.com/discovery/item/" + work_id,
                                                 "xhs",
                                                 uuid,
                                                 comment)
            except Exception as e:
                print(e)
