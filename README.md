UV 是一个由 **Astral** 公司开发的**超高速 Python 包管理工具**（用 Rust 编写），旨在替代 `pip` 和 `pip-tools`（如 `pip-compile`）。它专注于**极速的依赖安装和解析**，特别适合大型项目。以下是 UV 的核心用法详解：

---

### **1. 安装 UV**
```bash
# 通过 pipx 安装（推荐）
pipx install uv

# 或直接通过 pip 安装
pip install uv
```

---

### **2. 核心功能与命令**
#### **(1) 安装包（替代 `pip install`）**
```bash
# 安装单个包
uv pip install numpy

# 从 requirements.txt 安装
uv pip install -r requirements.txt

# 安装当前项目（基于 pyproject.toml）
uv pip install .
```

#### **(2) 生成锁定文件（替代 `pip-compile`）**
```bash
# 从 pyproject.toml 生成 requirements.txt
uv pip compile pyproject.toml -o requirements.txt

# 生成带哈希校验的严格依赖（生产环境推荐）
uv pip compile pyproject.toml -o requirements.txt --all-extras --strict
```

#### **(3) 同步依赖（按锁定文件精确安装）**
```bash
uv pip sync requirements.txt  # 强制环境与 requirements.txt 一致
```

#### **(4) 虚拟环境管理**
```bash
# 创建虚拟环境（默认 .venv）
uv venv

# 指定 Python 版本（需已安装）
uv venv --python 3.11

# 激活环境（根据系统）
# Linux/macOS
source .venv/bin/activate
# Windows
.\.venv\Scripts\activate
```

---

### **3. 工作流示例**
#### **新项目初始化**
```bash
# 创建虚拟环境
uv venv
source .venv/bin/activate

# 安装依赖
uv pip install fastapi uvicorn

# 生成锁定文件
uv pip compile -o requirements.txt
```

#### **现有项目同步**
```bash
uv venv --python 3.10  # 创建环境
uv pip sync requirements.txt  # 安装所有依赖
```

---

### **4. 性能对比**
| 操作                | UV 耗时     | 传统 pip 耗时 |
|---------------------|------------|--------------|
| 安装 numpy+pandas   | ~2s        | ~10s         |
| 大型项目依赖解析     | ~0.5s      | ~15s         |

---

### **5. 高级技巧**
- **加速安装**：用 `--no-cache` 禁用缓存（首次安装不推荐）
- **替换镜像源**：
  ```bash
  uv pip install numpy -i https://pypi.tuna.tsinghua.edu.cn/simple
  ```
- **仅生成锁定文件**（不安装）：
  ```bash
  uv pip compile pyproject.toml --dry-run -o requirements.txt
  ```

---

### **6. 注意事项**
1. **兼容性**：UV 支持 99% 的 `pip` 命令，但复杂私有源场景建议测试。
2. **锁定文件**：生产环境务必使用 `uv pip compile` 生成精确版本。
3. **虚拟环境**：UV 不强制使用虚拟环境，但**强烈推荐**（通过 `uv venv` 创建）。

> 💡 **提示**：UV 与 `pip` 和 `pip-tools` 的 CLI 设计兼容，大部分场景可直接替换原有命令。

---

通过 UV，依赖安装速度可提升 **5-10 倍**，尤其适合**CI/CD 流水线**和**大型 Monorepo 项目**。尝试用 UV 替换你的 `pip` 工作流，体验极速包管理！