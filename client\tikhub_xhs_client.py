# -*- coding: utf-8 -*-

import time
from typing import Optional

from common.logger import Logger
from utils.http_utils import HttpUtils
import requests


class TikhubXhsClient:
    def __init__(self,
                 logger_,
                 base_url='https://api.tikhub.io',
                 max_retries: int = 3,
                 retry_delay: int = 2,
                 api_key: str = "6p0MEmst+HkugHAX7XSqoUVvxgIWhozWj6tp4ib4mWFhqGtUdd4v9z+bXw=="):

        self.base_url = base_url
        self.authorization = api_key
        self.headers = {
            "accept": "application/json",
            "Authorization": f"Bearer {api_key}"
        }
        self._max_retries = max_retries
        self._retry_delay = retry_delay
        self.logger_ = logger_

    def __process_request(self, url: str, headers: dict, method: str, params: dict = None) -> Optional[dict]:

        for attempt in range(self._max_retries):
            try:
                if method == "GET":
                    response = HttpUtils.get(url=url, headers=headers)
                else:
                    response = HttpUtils.post(url, data=params, headers=headers)

                if not response or \
                        not response.text.strip() or \
                        not response.content:
                    error_message = "第 {0} 次响应内容为空, 状态码: {1}, URL:{2}".format(attempt + 1,
                                                                                         response.status_code,
                                                                                         response.url)
                    if self.logger_:
                        self.logger_.info(error_message)

                    if attempt < self._max_retries - 1:
                        if self.logger_:
                            self.logger_.info(f"等待 {self._retry_delay} 秒后进行第 {attempt + 2} 次重试...")
                        time.sleep(self._retry_delay)
                    continue

                response.raise_for_status()
                return response.json()

            except Exception as e:
                if self.logger_:
                    self.logger_.error(f"第 {attempt + 1} 次请求发生异常: {str(e)}, URL: {url}")
                if attempt < self._max_retries - 1:
                    if self.logger_:
                        self.logger_.info(f"等待 {self._retry_delay} 秒后进行第 {attempt + 2} 次重试...")
                    time.sleep(self._retry_delay)
                else:
                    return None

        if self.logger_:
            self.logger_.error(f"所有 {self._max_retries} 次重试都失败了，URL: {url}")
        return None

    def get_user_notes(self, user_id: str, cursor: str = None) -> dict:
        """
            查询用户笔记 v2
        """
        endpoint = "/api/v1/xiaohongshu/web_v2/fetch_home_notes"

        url = self.base_url + f"{endpoint}?user_id={user_id}&cursor"
        user_notes: dict = self.__process_request(url=url, headers=self.headers, method="GET")
        return user_notes

    def get_search_notes(self, keyword: str, page: int = 1, sort: str = "general", noteType: str = "0"):
        """
            获取搜索数据 v2
        """
        endpoint = "/api/v1/xiaohongshu/web_v2/fetch_search_notes"
        url: str = self.base_url + f"{endpoint}?keywords={keyword}&page={page}&sort_type={sort}&note_type={noteType}"

        search_result: dict = self.__process_request(url=url, headers=self.headers, method="GET")
        return search_result
