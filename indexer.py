# -*- coding: utf-8 -*-

import json
import os
import sys
import asyncio

from common.env import Env
from scene.xhs_account_listener import XhsAccountListener

cur_path = os.path.dirname(os.path.abspath(__file__))
project_path = os.path.split(cur_path)[0]
sys.path.append(cur_path)
sys.path.append(project_path)


logger_ = Env().get_main_logger()

if __name__ == "__main__":
    xhs_account_listener = XhsAccountListener(logger_)
    asyncio.run(xhs_account_listener.sync_new_articles())

