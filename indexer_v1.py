# -*- coding: utf-8 -*-

import json
import os
import sys
import asyncio

from client.qb.models import CommentResponse
from common.env import Env
from dao.repository.xhs_work_comment_repository import XhsWorkCommentRepository
from scene.xhs_account_listener import XhsAccountListener
from scene.xhs_keyword_processor import XhsKeywordProcessor
from scene.xhs_keyword_work_comment_requester import XhsKeywordWorkCommentRequester

cur_path = os.path.dirname(os.path.abspath(__file__))
project_path = os.path.split(cur_path)[0]
sys.path.append(cur_path)
sys.path.append(project_path)


logger_ = Env().get_main_logger()

if __name__ == "__main__":

    xhs_keyword_processor = XhsKeywordProcessor(logger_)
    # asyncio.run(xhs_keyword_processor.process())
    xhs_keyword_processor.sync_work_data()

