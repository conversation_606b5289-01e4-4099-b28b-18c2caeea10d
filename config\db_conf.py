# -*- coding: utf-8 -*-

import urllib
from urllib.parse import quote_plus as urlquote

from common.env import Env

'''
mysql数据库配置
'''
mysql_cfg_dict = {
    'dev': {
        'product_db_config': 'mysql://data:' + urllib.parse.quote(
            '@data-plus@^*^db123') + '@rm-f8zzo8z5t5ayp0oq0fo.mysql.rds.aliyuncs.com:3306/data_record?charset=utf8',
    },
    'pre': {
        'product_db_config': 'mysql://data:' + urllib.parse.quote(
            '@data-plus@^*^db123') + '@rm-f8zzo8z5t5ayp0oq0fo.mysql.rds.aliyuncs.com:3306/data_record?charset=utf8',
    },
    'prod': {
        'product_db_config': 'mysql://data:' + urllib.parse.quote(
            '@data-plus@^*^db123') + '@rm-f8zzo8z5t5ayp0oq0fo.mysql.rds.aliyuncs.com:3306/data_record?charset=utf8',
    }
}

mysql_cfg = mysql_cfg_dict.get(Env().get_env())
