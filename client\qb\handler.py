# -*- coding: utf-8 -*-

from utils.time_utils import TimeUtils
from client.qb.api_client import ApiClient
from client.qb.models import ApiConfig, Platform


class Handler:
    def __init__(self, logger_, platform: Platform):
        self.logger_ = logger_
        self._platform = platform

        # Initialize configuration
        config = ApiConfig(
            project_id=10312,
            sign="886dbdc01c9127c2d49af566ce3dd73e"
        )

        # Create API client
        self.client = ApiClient(config, logger_)

    def sync_data(self):
        pass

    def query_author_new_article_list(self, author_code, day=None) -> list:
        if day is None:
            day = TimeUtils.get_yst_datetime()

        if self._platform == "dy":
            return self.client.get_one_douyin_articles(day, author_code)
        elif self._platform == "xhs":
            return self.client.get_one_xhs_articles(day, author_code)
        elif self._platform == "wxvideo":
            return self.client.get_one_wxvideo_articles(day, author_code)

        return []

    def query_article_detail(self, key):
        try:
            return self.client.get_article_detail(self._platform, key)
        except Exception as e:
            self.logger_.error("query_article_detail error, %s", e)
            return None

    def query_article_comment(self, key, count: int) -> list:
        return self.client.get_article_comments(self._platform, key, count)
