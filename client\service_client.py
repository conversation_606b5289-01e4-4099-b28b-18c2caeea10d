# -*- coding: utf-8 -*-
from enum import Enum
from typing import Optional

from common.logger import Logger
from utils.http_utils import HttpUtils


class Environment(Enum):
    """API环境枚举"""

    LOCAL = "local"
    TEST = "test"
    PRODUCTION = "production"


class ServiceClient:
    """
    服务客户端类，用于调用外部API接口
    支持本地、测试、生产三种环境
    """

    # 环境配置映射
    ENV_CONFIG = {
        Environment.LOCAL: "http://127.0.0.1:9051",
        Environment.TEST: "https://apitest.jinsuosuo.com/core",
        Environment.PRODUCTION: "https://api.jinsuosuo.com/core",
    }

    def __init__(
            self,
            environment: Environment = Environment.LOCAL,
            # api_token: str = "api-token-71726a26c98b6a63a9bc9cac43ecab13",
            api_token: str = "api-token-a8cabb2bf81f04c8284c63db01514d0f",
            logger_: Logger = None,
    ):
        """
        初始化服务客户端

        Args:
            environment: API环境
            api_token: API认证令牌
            logger_: 日志记录器
        """
        self.base_url = self.ENV_CONFIG[environment]
        self.api_token = api_token
        self.headers = {"Authorization": api_token}
        self.logger_ = logger_
        self.environment = environment

        if self.logger_:
            self.logger_.info(
                f"ServiceClient initialized with environment: {environment.value}, base_url: {self.base_url}"
            )

    def query_can_update(self, platform: str) -> Optional[dict]:
        """
        查询是否可以更新的接口

        Args:
            user_id: 用户ID
            platform: 平台标识

        Returns:
            API响应数据或None

        Example:
            client = ServiceClient(Environment.LOCAL)
            result = client.query_can_update(user_id=9, platform="xhs")
        """
        endpoint = "/api/external/queryCanUpdate"

        # 构建查询参数
        params = {"platform": platform}

        url = self.base_url + endpoint

        if self.logger_:
            self.logger_.info(f"调用queryCanUpdate接口: URL={url}, params={params}")

        try:
            response = HttpUtils.get(url=url, headers=self.headers, params=params)

            if not response:
                if self.logger_:
                    self.logger_.error("请求失败，响应为空")
                return None

            if not response.text.strip():
                if self.logger_:
                    self.logger_.error("响应内容为空")
                return None

            response.raise_for_status()
            result = response.json()

            if self.logger_:
                self.logger_.info(f"queryCanUpdate接口调用成功: {result}")

            return result

        except Exception as e:
            if self.logger_:
                self.logger_.error(f"queryCanUpdate接口调用异常: {str(e)}")
            return None

    def is_response_success(self, response: dict) -> bool:
        """
        检查API响应是否成功

        Args:
            response: API响应数据

        Returns:
            是否成功
        """
        if not response:
            return False
        return response.get("code") == "success"

    def get_response_data(self, response: dict) -> Optional[list]:
        """
        从API响应中提取结果数据

        Args:
            response: API响应数据

        Returns:
            结果数据列表或None
        """
        if not self.is_response_success(response):
            return None
        return response.get("result", [])

    def mark_update(self, id: int) -> Optional[dict]:
        """
        标记更新成功的接口
        """
        endpoint = "/api/external/markUpdated"

        # 构建查询参数
        params = {"id": id}

        url = self.base_url + endpoint

        if self.logger_:
            self.logger_.info(f"调用markUpdate接口: URL={url}, params={params}")

        try:
            response = HttpUtils.get(url=url, headers=self.headers, params=params)

            if not response:
                if self.logger_:
                    self.logger_.error("markUpdate请求失败，响应为空")
                return None

            if not response.text.strip():
                if self.logger_:
                    self.logger_.error("markUpdate响应内容为空")
                return None

            response.raise_for_status()
            result = response.json()

            if self.logger_:
                self.logger_.info(f"markUpdate接口调用成功: {result}")

            return result

        except Exception as e:
            if self.logger_:
                self.logger_.error(f"markUpdate接口调用异常: {str(e)}")
            return None


if __name__ == "__main__":
    service = ServiceClient(environment=Environment.LOCAL)
    # pso = service.mark_update()
    # print(pso)
