# -*- coding: utf-8 -*-

from client.dingtalk_linker import Ding<PERSON>alk<PERSON>inker
from client.qb.handler import Handler
from client.qb.models import Platform
from dao.repository.xhs_keyword_article_record_repository import XhsKeywordArticleRecordRepository
from dao.repository.xhs_keyword_status_record_repository import XhsKeywordStatusRecordRepository
from dao.repository.xhs_work_comment_repository import XhsWorkCommentRepository
from dao.repository.xhs_work_detail_record_repository import XhsWorkDetailRecordRepository
from scene.data_sync import DingTalkDataSync
from scene.dingtalk_alerter import DingTalkAlerter
from scene.xhs_keyword_searcher_v1 import XhsKeywordSearcherV1
from scene.xhs_keyword_work_comment_requester import XhsKeywordWorkCommentRequester
from scene.xhs_keyword_work_detail_requester import XhsKeywordWorkDetailRequester


class XhsKeywordProcessor:
    def __init__(self, logger_):
        self.logger_ = logger_

        self.dingtalk_linker = DingTalkLinker(logger_)
        self.ding_talk_alert = DingTalkAlerter()
        self.xhs_work_repo = XhsWorkDetailRecordRepository()
        self.xhs_keyword_status = XhsKeywordStatusRecordRepository()
        self.xhs_keyword_article = XhsKeywordArticleRecordRepository()

        self.xhs_keyword_searcher = XhsKeywordSearcherV1(logger_)
        self.qingbo_handler = Handler(logger_=logger_, platform=Platform.XIAOHONGSHU)

        self.xhs_keyword_work_detail_requester = XhsKeywordWorkDetailRequester(logger_)
        self.xhs_keyword_work_comment_requester = XhsKeywordWorkCommentRequester(logger_)

    async def process(self):
        # 1. 关键字查询
        keyword_list = ['宫保丁丁牛饭', '宫保丁丁猪饭', '素鸡', '素牛排', '莲藕排骨汤',
                        '腾椒鸡', '树番茄汁', '玉米露', '肉松蒸面包', '杂粮饭']

        for keyword in keyword_list:
            # 查询数据存储数据库
            await self.xhs_keyword_searcher.search_key_word_v1(keyword, 200)

        record_ids = ["20250608223616", "20250608224009",
                      "20250608224253", "20250608224558",
                      "20250608224825", "20250608225144",
                      "20250608225424", "20250608225641",
                      "20250608225906", "20250608230259"
                      ]

        for record_id in record_ids:
            self.xhs_keyword_work_detail_requester.update_work_detail(record_id)

    async def process_comment(self):
        """
        keyword_list = ['宫保丁丁牛饭', '宫保丁丁猪饭', '素鸡', '素牛排', '莲藕排骨汤',
                        '腾椒鸡', '树番茄汁', '玉米露', '肉松蒸面包', '杂粮饭']

        for keyword in keyword_list:
            # 查询数据存储数据库
            await self.xhs_keyword_searcher.search_key_word_v1(keyword, 200)
        """
        record_ids = ["20250608223616", "20250608224009",
                      "20250608224253", "20250608224558",
                      "20250608224825", "20250608225144",
                      "20250608225424", "20250608225641",
                      "20250608225906", "20250608230259"
                      ]

        for record_id in record_ids:
            self.xhs_keyword_work_comment_requester.update_work_comment(record_id)

    def sync_work_data(self):
        xhs_work_comment_rep = XhsWorkCommentRepository()

        """
        webhook = "https://connector.dingtalk.com/webhook/flow/e274a7209282f448e24a9ab9"
        dentry_uuid = "LeBq413JAwKP5v07cn973eDvWDOnGvpb"
        id_or_name = "作品数据"

        data_sync = DingTalkDataSync(self.logger_, webhook, dentry_uuid, id_or_name)
        """

        import pandas as pd
        record_ids = ["20250608223616", "20250608224009",
                      "20250608224253", "20250608224558",
                      "20250608224825", "20250608225144",
                      "20250608225424", "20250608225641",
                      "20250608225906", "20250608230259"
                      ]

        records = []

        for record_id in record_ids:
            rows = self.xhs_keyword_article.query_work_detail_list(record_id)

            keyword = ''
            for row in rows:
                work_id = row['work_id']
                keyword = row['keyword']

                work_list = self.xhs_work_repo.query_work_detail(work_id)
                work=work_list[0]

                entity_list = xhs_work_comment_rep.query_comment_list(work_id)
                for entity in entity_list:
                    records.append({
                        "平台": "小红书",
                        "关键字": keyword,
                        "标题": work['title'],
                        "作品链接": entity['work_url'],
                        "评论人": entity['commenter_name'],
                        "评论内容": entity['content'],
                        "评论人首页": entity['commenter_url'],
                        "评论时间": entity['publish_time'],
                        "IP地址": entity['location_ip'],
                        "点赞数": entity['like_count'],
                    })

        # 假设 records 已经有数据了
        df = pd.DataFrame(records)

        # 导出到 Excel 文件
        df.to_excel(f"e://output.xlsx", index=False)


