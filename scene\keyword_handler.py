# -*- coding: utf-8 -*-

from config.xhs_keyword_config import keyword_task_dict
from scene.data_sync import DingTalkDataSync, LarkBitableDataSync
from scene.xhs_keyword_searcher import XhsKeywordSearcher


class KeyWordHandler:
    def __init__(self, logger_):
        self.logger_ = logger_
        self.xhs_keyword_searcher = XhsKeywordSearcher(logger_)

    async def invoke_task(self, task_id):
        task_info_dict = keyword_task_dict.get(task_id)
        if task_info_dict is None:
            self.logger_.error(f"KeyWordHandler task info is None, task_id={task_id}")

        keyword_list = task_info_dict.get("keywords")
        handler = task_info_dict.get("handler")

        data_sync = None
        if handler == 'dingtalk-bitable-linker':
            bitable = task_info_dict.get("bitable")

            webhook = bitable.get("webhook")
            dentry_uuid = bitable.get("dentryUuid")
            id_or_name = bitable.get("idOrName")

            data_sync = DingTalkDataSync(self.logger_, webhook, dentry_uuid, id_or_name)
        elif handler == 'lark-bitable':
            bitable = task_info_dict.get("bitable")

            app_id = bitable.get("app_id")
            app_secret = bitable.get("app_secret")
            app_token = bitable.get("app_token")
            article_table_id = bitable.get("article_table_id")

            data_sync = LarkBitableDataSync(self.logger_, app_id, app_secret, app_token, article_table_id)

        count = task_info_dict.get("count")

        for keyword in keyword_list:
            await self.xhs_keyword_searcher.search_key_word(keyword, count, data_sync)

