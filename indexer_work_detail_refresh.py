# -*- coding: utf-8 -*-
import time
from datetime import datetime

from client.service_client import Environment
from common.env import Env
from common.logger import Logger
from scene.work_detail_processor import WorkDetailProcessor

logger: Logger = Env().get_main_logger()


def run_processor():
    try:
        logger.info("开始执行小红书数据更新任务...")
        processor = WorkDetailProcessor(env=Environment.PRODUCTION, logger=logger)
        processor.start_job()
        logger.info("小红书数据更新任务执行完成")
    except Exception as e:
        logger.error(f"执行任务时发生错误: {str(e)}")


def main():
    logger.info("启动小红书数据更新定时任务")

    while True:
        try:
            current_time = datetime.now()
            logger.info(f"当前时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")

            run_processor()

            logger.info("等待半小时后执行下一次更新...")
            time.sleep(1800)

        except KeyboardInterrupt:
            logger.info("程序被手动终止")
            break
        except Exception as e:
            logger.error(f"发生未预期的错误: {str(e)}")
            time.sleep(60)


if __name__ == "__main__":
    main()
