# -*- coding: utf-8 -*-

import json
from typing import List, Dict, Any, Optional

import requests

from client.qb.handler import Handler
from client.qb.models import Platform, ArticleDetail
from client.service_client import ServiceClient, Environment
from common.env import Env
from common.logger import Logger
from dao.model.work_detail_refresh import WorkDetailRefreshRecord
from dao.repository.xhs_work_detail_refresh_repository import (
    XhsWorkDetailRefreshRepository,
)
from utils.time_utils import TimeUtils


class XhsDetailUpdater:
    """
    小红书作品详情更新
    """

    def __init__(
            self,
            environment: Environment = Environment.LOCAL,
            platform: Platform = Platform.XIAOHONGSHU,
            api_token: str = "api-token-a8cabb2bf81f04c8284c63db01514d0f",
            dingtalk_webhook: str = None,
            logger_: Logger = None,
    ):

        self.platform = platform
        self.logger_ = logger_

        # 初始化服务客户端
        self.service_client = ServiceClient(
            environment=environment, logger_=self.logger_, api_token=api_token
        )

        self.handler = Handler(logger_=self.logger_, platform=platform)

        self.refresh_repository = XhsWorkDetailRefreshRepository()

        self.dingtalk_webhook = dingtalk_webhook
        self.logger_.info("XhsDetailUpdater 初始化完成")

    def run(self) -> List[Dict]:

        """
        执行完整的更新流程

        Returns:
            List[Dict[str, Any]]: 处理结果列表
        """

        try:
            self.logger_.info("---- 开始执行详情更新流程 ---")

            # 批次生成ID
            record_id = TimeUtils.get_current_ts('%Y%m%d%H%M%S')

            tasks = self._query_tasks()
            if not tasks:
                self.logger_.info("没有找到待更新的任务")
                return []

            self.logger_.info(f"找到 {len(tasks)} 个待更新任务")

            # 2. 插入任务记录到数据库
            self._insert_tasks_to_db(tasks, record_id=record_id)

            detail_list: List[Dict] = []

            for task in tasks:
                try:
                    result: Dict = self._process_task(task=task, record_id=record_id)
                    if result:
                        detail_list.append(result)
                except Exception as e:
                    self.logger_.error(f"处理任务失败: {task}, 错误: {e}")
                    continue

            self.logger_.info(f"更新流程完成，成功处理 {len(detail_list)} 个任务")
            return detail_list

        except Exception as e:
            self.logger_.error(f"执行更新流程异常: {e}")
            raise

    def _query_tasks(self) -> List[Dict[str, Any]]:
        """
        查询可更新的任务列表

        Returns:
            List[Dict[str, Any]]: 任务列表
        """
        try:
            self.logger_.info(
                f"查询可更新任务， 平台: {self.platform.value}"
            )

            response = self.service_client.query_can_update(platform=self.platform.value)

            if not response:
                self.logger_.warning("查询任务列表失败：响应为空")
                return []

            # 检查响应是否成功
            if not self.service_client.is_response_success(response):
                self.logger_.error(
                    f"查询任务列表失败：{response.get('msg', '未知错误')}"
                )
                return []

            # 获取任务数据
            tasks = self.service_client.get_response_data(response)
            if not tasks:
                self.logger_.info("没有待更新的任务")
                return []

            self.logger_.info(f"成功获取 {len(tasks)} 个任务")
            return tasks

        except Exception as e:
            self.logger_.error(f"查询任务列表异常: {e}")
            return []

    def _insert_tasks_to_db(self, tasks: List[Dict[str, Any]], record_id: str) -> None:
        """
        将任务插入到数据库

        Args:
            tasks: 任务列表
        """
        try:
            self.logger_.info(f"开始插入 {len(tasks)} 个任务到数据库")

            for task in tasks:
                row_id = task.get("rowId")
                work_url = task.get("workUrl")
                work_id = task.get("workId")

                if not all([row_id, work_url, work_id]):
                    self.logger_.warning(f"任务数据不完整，跳过: {task}")
                    continue

                try:
                    # 插入任务记录
                    task_id = self.refresh_repository.insert_refresh_task(
                        record_id=record_id,
                        work_url=work_url,
                        work_id=work_id,
                        platform=self.platform.value,
                    )

                    self.logger_.info(
                        f"成功插入任务: id={task_id}, work_id={work_id}")

                except Exception as e:
                    self.logger_.error(f"插入任务失败: {task}, 错误: {e}")
                    continue

            self.logger_.info("任务插入完成")

        except Exception as e:
            self.logger_.error(f"插入任务到数据库异常: {e}")
            raise

    def _process_task(self, task: Dict[str, Any], record_id: str) -> Optional[Dict]:
        """
        处理单个任务
        """
        try:
            id = task.get("id")
            row_id = task.get("rowId")
            work_url = task.get("workUrl")
            work_id = task.get("workId")

            self.logger_.info(f"开始处理任务: work_id={work_id}, id={id}")

            work_format_url = work_url

            if self.platform.value == "dy":
                work_format_url = f"https://www.douyin.com/video/{work_id}"

            article_detail: ArticleDetail = self.handler.query_article_detail(work_format_url)

            if not article_detail:
                self.logger_.warning(f"获取作品详情失败: work_id={work_id}")
                return None

            self.logger_.info(f"成功获取作品详情: work_id={work_id}")

            detail_record = self._build_detail_record(
                record_id=record_id, article_detail=article_detail
            )
            record_id_db = self.refresh_repository.insert_refresh_record(detail_record)
            self.logger_.info(f"成功插入详情记录: record_id={record_id_db}")

            self.refresh_repository.update_refresh_task_status(
                record_id=record_id, work_id=work_id, status=1
            )
            self.logger_.info(f" == 更新任务状态成功 == record_id：{record_id}, work_id:{work_id} ")
            mark_result = self.service_client.mark_update(id)
            if mark_result and self.service_client.is_response_success(mark_result):
                self.logger_.info(f"成功标记任务完成: id={id}")
            else:
                self.logger_.warning(f"标记任务完成失败: id={id}")

            # 5. 构建返回结果
            res_dict = article_detail.to_dict()
            res_dict["row_id"] = row_id
            # self.logger_.info(f"任务处理完成: {res_dict}")
            return res_dict

        except Exception as e:
            self.logger_.error(f"处理任务异常: {task}, 错误: {e}")
            return None

    def _build_detail_record(
            self, record_id: str, article_detail: ArticleDetail
    ) -> WorkDetailRefreshRecord:
        """
        构建详情记录对象

        Args:
            row_id: 行ID
            article_detail: 文章详情对象

        Returns:
            WorkDetailRefreshRecord: 详情记录对象
        """
        current_time = TimeUtils.get_current_ts()

        return WorkDetailRefreshRecord(
            id=None,  # 自动生成
            record_id=record_id,
            platform=self.platform.value,  # 添加platform字段
            author_id=article_detail.media_id,
            author_identity=article_detail.media_identity,
            author_avatar=article_detail.media_picurl,
            author_name=article_detail.media_name,
            author_url="",
            work_id=article_detail.news_uuid,
            work_uuid=article_detail.news_uuid,
            url=article_detail.news_url,
            download_url=getattr(article_detail, "download_url", None),
            long_url=getattr(article_detail, "long_url", None),
            digest=getattr(article_detail, "news_digest", None),
            title=getattr(article_detail, "news_title", None),
            thumbnail_link=getattr(article_detail, "news_thumbnail_link", None),
            content=getattr(article_detail, "news_content", None),
            img_urls=getattr(article_detail, "img_urls", None),
            video_urls=getattr(article_detail, "video_urls", None),
            music_url=getattr(article_detail, "music_url", None),
            music_author_name=getattr(article_detail, "music_author_name", None),
            music_id=getattr(article_detail, "music_id", None),
            music_name=getattr(article_detail, "music_name", None),
            publish_time=getattr(article_detail, "news_posttime", None),
            publish_day=getattr(article_detail, "publish_day", None),
            location_ip=getattr(article_detail, "news_content_ip_location", None),
            read_count=getattr(article_detail, "news_read_count", 0),
            like_count=getattr(article_detail, "news_like_count", 0),
            comment_count=getattr(article_detail, "news_comment_count", 0),
            share_count=getattr(article_detail, "news_reposts_count", 0),
            collect_count=getattr(article_detail, "news_collect_cnt", 0),
            record_time=current_time,
            is_del=0,
        )

    def sync_to_dingtalk(self, results: List[Dict]) -> None:
        """
        同步数据到钉钉

        Args:
            results: 处理结果列表
        """
        try:
            self.logger_.info(f"开始同步 {len(results)} 条数据到钉钉")

            # 构建钉钉请求数据
            payload = {"data": results}

            headers = {"Content-Type": "application/json"}

            # 发送请求
            response = requests.post(
                self.dingtalk_webhook,
                headers=headers,
                data=json.dumps(payload),
                timeout=30,
            )

            if response.status_code == 200:
                self.logger_.info("成功同步数据到钉钉")
                self.logger_.info(f"钉钉响应: {response.text}")
            else:
                self.logger_.error(
                    f"同步数据到钉钉失败: status_code={response.status_code}, response={response.text}"
                )

        except Exception as e:
            self.logger_.error(f"同步数据到钉钉异常: {e}")


# 使用示例
if __name__ == "__main__":
    # 创建更新器实例
    updater = XhsDetailUpdater(environment=Environment.LOCAL, platform=Platform.XIAOHONGSHU)

    # 执行更新流程
    results = updater.run()

    print(f"处理完成，共处理 {len(results)} 个任务")
