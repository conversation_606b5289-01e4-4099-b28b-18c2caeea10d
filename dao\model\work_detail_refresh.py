# -*- coding: utf-8 -*-
# @Time : 2025/6/9 17:53
# <AUTHOR> cyf
# @File : work_detail_refresh.py


class WorkDetailRefreshTask:
    """
    刷新任务表
    """

    def __init__(
            self,
            id,
            company=None,
            work_url=None,
            work_id=None,
            row_id=None,
            platform="xhs",
            status=0,
            create_time=None,
    ):
        self.id = id
        self.company = company
        self.work_url = work_url
        self.work_id = work_id
        self.platform = platform
        self.row_id = row_id
        self.status = status
        self.create_time = create_time


class WorkDetailRefreshRecord:
    """
    刷新记录表
    """

    def __init__(
            self,
            id,
            record_id=None,
            platform="xhs",
            author_id=None,
            author_identity=None,
            author_avatar=None,
            author_name=None,
            author_url=None,
            work_id=None,
            work_uuid=None,
            url=None,
            download_url=None,
            long_url=None,
            digest=None,
            title=None,
            thumbnail_link=None,
            content=None,
            img_urls=None,
            video_urls=None,
            music_url=None,
            music_author_name=None,
            music_id=None,
            music_name=None,
            publish_time=None,
            publish_day=None,
            location_ip=None,
            read_count=None,
            like_count=None,
            comment_count=None,
            share_count=None,
            collect_count=None,
            record_time=None,
            is_del=0,
    ):
        self.id = id
        self.record_id = record_id
        self.platform = platform
        self.author_id = author_id
        self.author_identity = author_identity
        self.author_avatar = author_avatar
        self.author_name = author_name
        self.author_url = author_url
        self.work_id = work_id
        self.work_uuid = work_uuid
        self.url = url
        self.download_url = download_url
        self.long_url = long_url
        self.digest = digest
        self.title = title
        self.thumbnail_link = thumbnail_link
        self.content = content
        self.img_urls = img_urls
        self.video_urls = video_urls
        self.music_url = music_url
        self.music_author_name = music_author_name
        self.music_id = music_id
        self.music_name = music_name
        self.publish_time = publish_time
        self.publish_day = publish_day
        self.location_ip = location_ip
        self.read_count = read_count
        self.like_count = like_count
        self.comment_count = comment_count
        self.share_count = share_count
        self.collect_count = collect_count
        self.record_time = record_time
        self.is_del = is_del
