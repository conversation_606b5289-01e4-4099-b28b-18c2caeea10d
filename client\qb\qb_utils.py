# -*- coding: utf-8 -*-

from client.qb.models import (
    ArticleDetail,
    CommentResponse,
    DouyinGroupResponse,
    WxVideoGroupResponse,
    XiaohongshuGroupResponse
)


def print_article_detail(article: ArticleDetail) -> None:
    """打印文章详情的辅助函数"""
    print("\n文章详情:")
    print(f"标题: {article.news_title}")
    print(f"平台: {article.platform_name}")
    print(f"作者: {article.media_name}")
    print(f"发布时间: {article.news_posttime}")
    print(f"IP位置: {article.news_content_ip_location}")
    print(f"阅读数: {article.news_read_count}")
    print(f"点赞数: {article.news_like_count}")
    print(f"评论数: {article.news_comment_count}")
    print(f"收藏数: {article.news_collect_cnt}")
    print(f"转发数: {article.news_reposts_count}")
    print(f"\n文章内容:\n{article.news_content}")


def print_comments(comment_response: CommentResponse) -> None:
    """打印评论数据的辅助函数"""
    print("\n评论数据:")
    for i, comment in enumerate(comment_response.comments, 1):
        print(f"\n评论 {i}:")
        print(f"用户: {comment.comment_user}")
        print(f"内容: {comment.comment_content}")
        print(f"时间: {comment.comment_posttime}")
        print(f"点赞数: {comment.comment_like_count}")
        print(f"IP位置: {comment.comment_ip_location}")

    if comment_response.has_more:
        print(f"\n还有更多评论，下一页游标: {comment_response.cursor}")


def print_douyin_group_articles(response: DouyinGroupResponse) -> None:
    """打印抖音分组文章列表"""
    print(f"\n抖音分组文章列表 (共{response.num_found}条):")
    for i, article in enumerate(response.articles, 1):
        print(f"\n文章 {i}:")
        print(f"标题: {article.news_title}")
        print(f"作者: {article.douyin_name} (抖音号: {article.douyin_code})")
        print(f"发布时间: {article.news_posttime}")
        print(f"点赞数: {article.like_nums}")
        print(f"评论数: {article.comment_nums}")
        print(f"收藏数: {article.collection_count}")
        print(f"分享数: {article.share_nums}")
        print(f"IP位置: {article.news_content_ip_location}")
        print(f"链接: {article.news_url}")


def print_wxvideo_group_articles(response: WxVideoGroupResponse) -> None:
    """打印微信视频号分组文章列表"""
    print(f"\n微信视频号分组文章列表 (共{response.num_found}条):")
    for i, article in enumerate(response.articles, 1):
        print(f"\n文章 {i}:")
        print(f"标题: {article.news_title}")
        print(f"作者: {article.wxvideo_name}")
        print(f"发布时间: {article.news_posttime}")
        print(f"点赞数: {article.news_like_count}")
        print(f"评论数: {article.news_comment_count}")
        print(f"转发数: {article.news_reposts_count}")
        print(f"链接: {article.news_url}")
        print(f"EID: {article.eid}")


def print_xiaohongshu_group_articles(response: XiaohongshuGroupResponse) -> None:
    """打印小红书分组文章列表"""
    print(f"\n小红书分组文章列表 (共{response.num_found}条):")
    for i, article in enumerate(response.articles, 1):
        print(f"\n文章 {i}:")
        print(f"标题: {article.news_title}")
        print(f"作者: {article.xiaohongshu_name}")
        print(f"发布时间: {article.news_posttime}")
        print(f"评论数: {article.news_comment_count}")
        print(f"点赞数: {article.news_like_count}")
        print(f"收藏数: {article.news_collect_count}")
        print(f"是否原创: {'是' if article.news_is_origin == '1' else '否'}")
        print(f"是否视频: {'是' if article.is_video == '1' else '否'}")
        print(f"链接: {article.news_url}")
        if article.news_img_urls:
            print("图片链接:")
            for url in article.news_img_urls.split(';'):
                if url:
                    print(f"  - {url}")
