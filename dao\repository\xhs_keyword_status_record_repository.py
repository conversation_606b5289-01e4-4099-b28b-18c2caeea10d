# -*- coding: utf-8 -*-

from dao.db_adapter import g_database_product


class XhsKeywordStatusRecordRepository(object):
    """
    对应表格 bardata 操作
    """

    def __init__(self):
        self.gdb_datasource = g_database_product

    def insert(self, keyword, record_id, status):
        sql = '''
            INSERT INTO `xhs_keyword_status_record`(`keyword`, `record_id`, `status`) 
            VALUES (%s,%s,%s);
            '''

        args = (keyword, record_id, status)
        return self.gdb_datasource.insert(sql=sql, args=args)

    def update(self, record_id, status):
        sql = 'update xhs_keyword_status_record set `status`=%s where record_id=%s'
        return self.gdb_datasource.execute(sql, (status, record_id))
