# -*- coding: utf-8 -*-

import json

from client.dingtalk_linker import DingTalkLinker
from client.lark_bitable import LarkBitable
from dao.model.author_work import AuthorWork
from utils.http_utils import HttpUtils


class DataSync(object):
    def invoke(self, keyword, articles: list[AuthorWork]):
        pass


class DingTalkDataSync(DataSync):
    def __init__(self, logger_, webhook, dentry_uuid, id_or_name):
        self.logger_ = logger_

        self.dingtalk_linker = DingTalkLinker(logger_)

        self.webhook = webhook
        self.dentry_uuid = dentry_uuid
        self.id_or_name = id_or_name

    def invoke(self, keyword, articles: list[AuthorWork]):
        try:
            data = self._build_notify_body(keyword, articles, self.dentry_uuid, self.id_or_name)
            self.dingtalk_linker.send_dingtalk_webhook_post(self.webhook, data)
        except Exception as e:
            self.logger_.error(f"_notify_webhook error: {e}")

    def _build_notify_body(self, keyword, articles: list[AuthorWork], dentry_uuid, id_or_name):
        records = []
        for article in articles:
            records.append({
                "平台": "小红书",
                "关键词": keyword,
                "标题": article.title,
                "作品链接": article.url,
                "作者昵称": article.author_name,
                "作者主页": article.author_url,
                "正文": article.content,
                "图文链接": article.thumbnail_link,
                "IP地址": article.location_ip,
                "点赞数": article.like_count,
                "评论数": article.comment_count,
                "转发数": article.share_count,
                "收藏数": article.collect_count,
                "发布时间字符串": article.publish_time,
                "UID": article.work_id
            })

        # 您提供的请求体参数
        return {
            "dentryUuid": dentry_uuid,
            "idOrName": id_or_name,
            "records": records
        }


class LarkBitableDataSync(DataSync):
    def __init__(self, logger_, app_id, app_secret, app_token, article_table_id):
        self.logger_ = logger_

        self.app_id = app_id
        self.app_secret = app_secret
        self.app_token = app_token
        self.article_table_id = article_table_id

        self.lark_bitable = LarkBitable(logger_, app_id, app_secret, app_token, article_table_id)

    def invoke(self, keyword, articles: list[AuthorWork]):
        try:
            self.lark_bitable.batch_insert_article_records(keyword, articles)
        except Exception as e:
            self.logger_.error(f"LarkBitableDataSync error, e={e}")
