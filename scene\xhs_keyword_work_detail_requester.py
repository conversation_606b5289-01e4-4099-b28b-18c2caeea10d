# -*- coding: utf-8 -*-
import time
from typing import Optional

from dateutil import parser

from client.dingtalk_linker import DingTalk<PERSON>inker
from client.qb.handler import Handler
from client.qb.models import Platform
from dao.model.author_work import AuthorWork
from dao.repository.xhs_keyword_article_record_repository import XhsKeywordArticleRecordRepository
from dao.repository.xhs_keyword_status_record_repository import XhsKeywordStatusRecordRepository
from dao.repository.xhs_work_detail_record_repository import XhsWorkDetailRecordRepository
from scene.dingtalk_alerter import DingTalkAlerter
from utils.string_utils import StringUtils
from utils.time_utils import TimeUtils


class XhsKeywordWorkDetailRequester:
    def __init__(self, logger_):
        self.logger_ = logger_

        self.ding_talk_alert = DingTalkAlerter()
        self.dingtalk_linker = DingTalkLinker(logger_)
        self.xhs_work_repo = XhsWorkDetailRecordRepository()
        self.xhs_keyword_status = XhsKeywordStatusRecordRepository()
        self.xhs_keyword_article = XhsKeywordArticleRecordRepository()

        self.qingbo_handler = Handler(logger_=logger_, platform=Platform.XIAOHONGSHU)

    def update_work_detail(self, record_id):
        rows = self.xhs_keyword_article.query_work_detail_list(record_id)
        for row in rows:
            try:
                work_id = row['work_id']
                nwork_list = self.xhs_work_repo.query_work_detail(work_id)
                if len(nwork_list) == 0:
                    continue

                nwork = nwork_list[0]
                is_detail = nwork['is_detail']
                if is_detail == 1:
                    continue

                work = self.query_work_detail_by_qingbo(work_id)
                if work is not None:
                    # 更新数据库
                    self.xhs_work_repo.update_work_detail_status(content=work.content,
                                                                 read_count=0,
                                                                 like_count=work.like_count,
                                                                 comment_count=work.comment_count,
                                                                 share_count=work.share_count,
                                                                 collect_count=work.collect_count,
                                                                 work_id=work.work_id,
                                                                 is_detail=1)
            except Exception as e:
                self.logger_.error("update_work_detail error={e}")

    def query_work_detail_by_qingbo(self, work_id):
        try:
            work_url = "https://www.xiaohongshu.com/discovery/item/" + work_id
            item = self.qingbo_handler.query_article_detail(key=work_url)
            if item is None:
                time.sleep(1)
                item = self.qingbo_handler.query_article_detail(key=work_url)

            if item is None:
                time.sleep(1)
                item = self.qingbo_handler.query_article_detail(key=work_url)

            if item:
                item_dict = item.to_dict()
                item_dict["id"] = work_id

                return self._build_article(item_dict)
        except Exception as e:
            self.logger_.error(f"获取笔记详情失败: {e}, note_id: {work_id}")
            return None

    def _build_article(self, item: dict) -> Optional[AuthorWork]:
        try:
            author_name = item.get('media_name', '')
            author_id = item.get('media_identity', '')
            author_avatar = item.get('media_picurl', '')
            author_url = "https://www.xiaohongshu.com/user/profile/" + author_id

            work_id = item.get('id', '')
            url = "https://www.xiaohongshu.com/discovery/item/" + work_id
            title = item.get('news_title', '')
            content = item.get('news_content')
            collect_count = item.get('news_collect_cnt')
            comment_count = item.get('news_comment_count')
            share_count = item.get('news_reposts_count')
            like_count = item.get('news_like_count')
            publish_time = item.get('news_posttime')

            date_obj = parser.parse(publish_time)
            publish_day = date_obj.strftime("%Y-%m-%d")
            thumbnail_link = item.get('news_headimg_url', '')

            return AuthorWork(_id=0, platform='xhs', author_id=author_id,
                              author_identity=author_id, author_avatar=author_avatar,
                              author_name=author_name, author_url=author_url,
                              work_id=work_id, work_uuid=work_id, url=url,
                              download_url='', long_url=url, digest=content,
                              title=title, thumbnail_link=thumbnail_link,
                              content=content, img_urls='', video_urls='', music_url='', music_author_name='',
                              music_id='', music_name='', publish_time=publish_time,
                              publish_day=publish_day, location_ip='', read_count=0,
                              like_count=like_count, comment_count=comment_count,
                              share_count=share_count, collect_count=collect_count,
                              text_polarity=-1,
                              record_time=TimeUtils.get_current_ts(),
                              is_detail=1
                              )
        except Exception as e:
            self.logger_.error(f"{StringUtils.obj_2_json_string(item)}, {e}")
            return None


    def _get_thumbnailLink(self, note):
        try:
            images_list = note.get('images_list')
            if len(images_list) > 0:
                image = images_list[0]
                return image.get('url')
        except Exception as e:
            return ''

    def _insert_keyword_work_record(self, keyword, record_id, work_id):
        self.xhs_keyword_article.insert(keyword, record_id, work_id)

    def _insert_keyword_status_record(self, keyword, record_id, status):
        self.xhs_keyword_status.insert(keyword, record_id, status)

    def _update_keyword_status_record(self, record_id, status):
        self.xhs_keyword_status.update(record_id, status)
