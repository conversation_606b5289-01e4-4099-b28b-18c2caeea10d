# -*- coding: utf-8 -*-

from dao.db_adapter import g_database_product


class XhsKeywordArticleRecordRepository(object):
    """
    对应表格 bardata 操作
    """
    def __init__(self):
        self.gdb_datasource = g_database_product

    def insert(self, keyword, record_id, word_id):
        sql = '''
            INSERT INTO `xhs_keyword_article_record`(
                `keyword`, `record_id`, `work_id`) 
            VALUES (%s,%s,%s);
            '''

        args = (keyword, record_id, word_id)
        return self.gdb_datasource.insert(sql=sql, args=args)

    def query_work_detail_list(self, record_id):
        sql = 'select * from xhs_keyword_article_record where record_id=%s'
        return self.gdb_datasource.fetch_all(sql, (record_id))

    def query_work_record(self, record_id, work_id):
        sql = 'select * from xhs_keyword_article_record where record_id=%s and work_id=%s '
        return self.gdb_datasource.fetch_all(sql, (record_id, work_id))

    def update_work_comment_status(self, record_id, work_id, status):
        sql = "update xhs_keyword_article_record set comment_status=%s " \
              "where record_id=%s and work_id=%s"
        return self.gdb_datasource.execute(sql, (status, record_id, work_id))
