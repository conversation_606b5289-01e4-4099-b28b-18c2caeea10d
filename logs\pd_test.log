2025-06-10 22:32:17.783 ERROR mysql_datasource.py insert Line:72 9764 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:32:41.357 ERROR mysql_datasource.py insert Line:72 9764 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:32:42.331 ERROR mysql_datasource.py insert Line:72 9764 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:32:44.196 ERROR mysql_datasource.py insert Line:72 9764 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:32:44.318 ERROR mysql_datasource.py insert Line:72 9764 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:32:44.440 ERROR mysql_datasource.py insert Line:72 9764 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:32:44.556 ERROR mysql_datasource.py insert Line:72 9764 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:32:44.671 ERROR mysql_datasource.py insert Line:72 9764 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:32:44.788 ERROR mysql_datasource.py insert Line:72 9764 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:32:44.904 ERROR mysql_datasource.py insert Line:72 9764 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:32:45.024 ERROR mysql_datasource.py insert Line:72 9764 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:32:45.144 ERROR mysql_datasource.py insert Line:72 9764 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:32:45.265 ERROR mysql_datasource.py insert Line:72 9764 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:32:45.384 ERROR mysql_datasource.py insert Line:72 9764 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:32:45.498 ERROR mysql_datasource.py insert Line:72 9764 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:32:45.616 ERROR mysql_datasource.py insert Line:72 9764 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:32:45.734 ERROR mysql_datasource.py insert Line:72 9764 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:32:45.854 ERROR mysql_datasource.py insert Line:72 9764 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:32:45.969 ERROR mysql_datasource.py insert Line:72 9764 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:32:46.091 ERROR mysql_datasource.py insert Line:72 9764 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:32:46.206 ERROR mysql_datasource.py insert Line:72 9764 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:32:46.324 ERROR mysql_datasource.py insert Line:72 9764 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:32:46.444 ERROR mysql_datasource.py insert Line:72 9764 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:32:46.559 ERROR mysql_datasource.py insert Line:72 9764 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:32:46.678 ERROR mysql_datasource.py insert Line:72 9764 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:32:46.792 ERROR mysql_datasource.py insert Line:72 9764 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:32:46.912 ERROR mysql_datasource.py insert Line:72 9764 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:36:43.362 ERROR mysql_datasource.py insert Line:72 25364 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:36:43.477 ERROR mysql_datasource.py insert Line:72 25364 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:36:43.586 ERROR mysql_datasource.py insert Line:72 25364 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:36:43.689 ERROR mysql_datasource.py insert Line:72 25364 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:36:43.792 ERROR mysql_datasource.py insert Line:72 25364 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:36:43.895 ERROR mysql_datasource.py insert Line:72 25364 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:36:43.994 ERROR mysql_datasource.py insert Line:72 25364 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:36:44.097 ERROR mysql_datasource.py insert Line:72 25364 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:36:44.199 ERROR mysql_datasource.py insert Line:72 25364 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:36:44.303 ERROR mysql_datasource.py insert Line:72 25364 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:36:44.415 ERROR mysql_datasource.py insert Line:72 25364 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:36:44.513 ERROR mysql_datasource.py insert Line:72 25364 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:36:44.611 ERROR mysql_datasource.py insert Line:72 25364 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:36:44.713 ERROR mysql_datasource.py insert Line:72 25364 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:36:44.812 ERROR mysql_datasource.py insert Line:72 25364 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:36:44.915 ERROR mysql_datasource.py insert Line:72 25364 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:36:45.017 ERROR mysql_datasource.py insert Line:72 25364 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:36:45.120 ERROR mysql_datasource.py insert Line:72 25364 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:36:45.227 ERROR mysql_datasource.py insert Line:72 25364 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:36:45.332 ERROR mysql_datasource.py insert Line:72 25364 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:36:45.440 ERROR mysql_datasource.py insert Line:72 25364 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:36:45.544 ERROR mysql_datasource.py insert Line:72 25364 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:36:45.648 ERROR mysql_datasource.py insert Line:72 25364 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:36:45.753 ERROR mysql_datasource.py insert Line:72 25364 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:36:45.856 ERROR mysql_datasource.py insert Line:72 25364 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:36:45.960 ERROR mysql_datasource.py insert Line:72 25364 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:36:46.062 ERROR mysql_datasource.py insert Line:72 25364 - insert db error, (1146, "Table 'data_record.work_detail_refresh_task' doesn't exist")
2025-06-10 22:37:20.608 ERROR mysql_datasource.py insert Line:72 25364 - insert db error, (1146, "Table 'data_record.work_detail_refresh_record' doesn't exist")
2025-06-11 22:27:22.214 INFO work_detail_processor.py start_job Line:27 5100 - start company :佳尔优优
2025-06-11 22:27:22.221 INFO work_detail_processor.py start_job Line:30 5100 - 公司：佳尔优优, 配置没有开启: ['task-update-article-001']
2025-06-11 22:27:22.221 INFO work_detail_processor.py start_job Line:27 5100 - start company :朱栈科技
2025-06-11 22:30:55.321 INFO work_detail_processor.py start_job Line:28 2696 - start company :佳尔优优
2025-06-11 22:30:55.321 INFO work_detail_processor.py start_job Line:31 2696 - 公司：佳尔优优, 配置没有开启: ['task-update-article-001']
2025-06-11 22:30:55.321 INFO work_detail_processor.py start_job Line:28 2696 - start company :朱栈科技
2025-06-11 22:30:55.322 INFO service_client.py __init__ Line:52 2696 - ServiceClient initialized with environment: production, base_url: https://api.jinsuosuo.com/core
2025-06-11 22:30:55.322 INFO xhs_detail_updater.py __init__ Line:49 2696 - XhsDetailUpdater 初始化完成
2025-06-11 22:30:55.322 INFO xhs_detail_updater.py run Line:61 2696 - ---- 开始执行详情更新流程 ---
2025-06-11 22:30:55.322 INFO xhs_detail_updater.py _query_tasks Line:102 2696 - 查询可更新任务， 平台: dy
2025-06-11 22:30:55.322 INFO service_client.py query_can_update Line:79 2696 - 调用queryCanUpdate接口: URL=https://api.jinsuosuo.com/core/api/external/queryCanUpdate, params={'platform': 'dy'}
2025-06-11 22:30:57.261 INFO service_client.py query_can_update Line:98 2696 - queryCanUpdate接口调用成功: {'code': '90002', 'result': '', 'msg': 'access token无效'}
2025-06-11 22:30:57.263 ERROR xhs_detail_updater.py _query_tasks Line:114 2696 - 查询任务列表失败：access token无效
2025-06-11 22:31:00.772 INFO xhs_detail_updater.py run Line:68 2696 - 没有找到待更新的任务
2025-06-11 22:31:00.772 INFO work_detail_processor.py __process Line:55 2696 - {'api-token': 'api-token-a8cabb2bf81f04c8284c63db01514d0f', 'user_id': 9}, 没有需要更新的数据
2025-06-11 22:39:53.415 INFO work_detail_processor.py start_job Line:28 8028 - start company :佳尔优优
2025-06-11 22:39:53.415 INFO work_detail_processor.py start_job Line:31 8028 - 公司：佳尔优优, 配置没有开启: ['task-update-article-001']
2025-06-11 22:39:53.416 INFO work_detail_processor.py start_job Line:28 8028 - start company :朱栈科技
2025-06-11 22:39:53.416 INFO service_client.py __init__ Line:52 8028 - ServiceClient initialized with environment: production, base_url: https://api.jinsuosuo.com/core
2025-06-11 22:39:53.416 INFO xhs_detail_updater.py __init__ Line:49 8028 - XhsDetailUpdater 初始化完成
2025-06-11 22:39:53.416 INFO xhs_detail_updater.py run Line:61 8028 - ---- 开始执行详情更新流程 ---
2025-06-11 22:39:53.416 INFO xhs_detail_updater.py _query_tasks Line:102 8028 - 查询可更新任务， 平台: dy
2025-06-11 22:39:53.416 INFO service_client.py query_can_update Line:79 8028 - 调用queryCanUpdate接口: URL=https://api.jinsuosuo.com/core/api/external/queryCanUpdate, params={'platform': 'dy'}
2025-06-11 22:40:13.445 INFO work_detail_processor.py start_job Line:28 10140 - start company :佳尔优优
2025-06-11 22:40:13.446 INFO work_detail_processor.py start_job Line:31 10140 - 公司：佳尔优优, 配置没有开启: ['task-update-article-001']
2025-06-11 22:40:13.446 INFO work_detail_processor.py start_job Line:28 10140 - start company :朱栈科技
2025-06-11 22:40:13.446 INFO service_client.py __init__ Line:52 10140 - ServiceClient initialized with environment: production, base_url: https://api.jinsuosuo.com/core
2025-06-11 22:40:13.446 INFO xhs_detail_updater.py __init__ Line:49 10140 - XhsDetailUpdater 初始化完成
2025-06-11 22:40:13.446 INFO xhs_detail_updater.py run Line:61 10140 - ---- 开始执行详情更新流程 ---
2025-06-11 22:40:13.446 INFO xhs_detail_updater.py _query_tasks Line:102 10140 - 查询可更新任务， 平台: dy
2025-06-11 22:40:13.446 INFO service_client.py query_can_update Line:79 10140 - 调用queryCanUpdate接口: URL=https://api.jinsuosuo.com/core/api/external/queryCanUpdate, params={'platform': 'dy'}
2025-06-11 22:40:15.137 INFO service_client.py query_can_update Line:98 10140 - queryCanUpdate接口调用成功: {'code': 'success', 'result': [{'id': 144, 'rowId': 'UPDgkSikV4', 'workUrl': 'https://www.douyin.com/video/7512407127186050315', 'workId': '7512407127186050315', 'userId': 121, 'platform': 'dy', 'submitTime': None, 'submitUser': None, 'status': 0, 'expireTime': '2025-06-12 00:00:00', 'nextUpdateTime': '2025-06-11 00:00:00'}], 'msg': '操作成功'}
2025-06-11 22:40:15.138 INFO xhs_detail_updater.py _query_tasks Line:125 10140 - 成功获取 1 个任务
2025-06-11 22:40:26.631 INFO xhs_detail_updater.py run Line:71 10140 - 找到 1 个待更新任务
2025-06-11 22:40:26.986 INFO xhs_detail_updater.py _insert_tasks_to_db Line:140 10140 - 开始插入 1 个任务到数据库
2025-06-11 22:40:27.210 INFO xhs_detail_updater.py _insert_tasks_to_db Line:160 10140 - 成功插入任务: id=1, work_id=7512407127186050315
2025-06-11 22:40:27.211 INFO xhs_detail_updater.py _insert_tasks_to_db Line:167 10140 - 任务插入完成
2025-06-11 22:41:09.329 INFO xhs_detail_updater.py _process_task Line:183 10140 - 开始处理任务: work_id=7512407127186050315, id=144
2025-06-11 22:41:11.685 INFO xhs_detail_updater.py _process_task Line:196 10140 - 成功获取作品详情: work_id=7512407127186050315
2025-06-11 22:41:14.423 INFO xhs_detail_updater.py _process_task Line:202 10140 - 成功插入详情记录: record_id=1
2025-06-11 22:41:18.935 INFO xhs_detail_updater.py _process_task Line:207 10140 -  == 更新任务状态成功 == record_id：20250611224013, work_id:7512407127186050315 
2025-06-11 22:41:19.104 INFO service_client.py mark_update Line:147 10140 - 调用markUpdate接口: URL=https://api.jinsuosuo.com/core/api/external/markUpdated, params={'id': 144}
2025-06-11 22:41:21.039 INFO service_client.py mark_update Line:166 10140 - markUpdate接口调用成功: {'code': 'success', 'result': '', 'msg': '操作成功'}
2025-06-11 22:41:22.801 INFO xhs_detail_updater.py _process_task Line:210 10140 - 成功标记任务完成: id=144
2025-06-11 22:41:22.801 INFO xhs_detail_updater.py run Line:87 10140 - 更新流程完成，成功处理 1 个任务
2025-06-11 22:41:24.467 INFO work_detail_processor.py __process Line:57 10140 - 获取到的作品列表为 : [{'news_uuid': '47f8efa1a14597029be4816738760725', 'news_url': 'https://www.douyin.com/video/7512407127186050315', 'platform_name': '抖音', 'media_identity': '4031231330103960', 'media_id': 'klxgt196', 'media_name': '快乐小狗兔🐰', 'news_video_urls': 'https://www.douyin.com/aweme/v1/play/?video_id=v0300fg10000d10mrm7og65th8ihhr10&line=0&file_id=e990581b4c1742e086911c8998c161db&sign=0bcffd4e8d6ec4a9c0b3940cf25fd31c&is_play_url=1&source=PackSourceEnum_AWEME_DETAIL', 'news_img_urls': '', 'news_title': '4min巨详细沉浸式全妆跟练！轻亚裔小猫妆🐈！日常又出片，俏皮感十足！全是细节新手姐妹快学起来！#妆教 #新手化妆教程 #妆容分享 #今日妆容 #美妆模范生', 'news_digest': '4min巨详细沉浸式全妆跟练！轻亚裔小猫妆🐈！日常又出片，俏皮感十足！全是细节新手姐妹快学起来！#妆教 #新手化妆教程 #妆容分享 #今日妆容 #美妆模范生', 'news_posttime': '2025-06-05 18:16:39', 'news_content_ip_location': '', 'news_content': '4min巨详细沉浸式全妆跟练！轻亚裔小猫妆🐈！日常又出片，俏皮感十足！全是细节新手姐妹快学起来！#妆教 #新手化妆教程 #妆容分享 #今日妆容 #美妆模范生', 'news_read_count': 0, 'news_like_count': 7033, 'news_comment_count': 239, 'news_collect_cnt': 2010, 'news_reposts_count': 433, 'news_headimg_url': 'https://p3-pc-sign.douyinpic.com/tos-cn-p-0015/oASfDf2EDAqoFxoAYCE3W6QRQBDQFExg7T9b3A~tplv-dy-360p.jpeg?lk3s=138a59ce&x-expires=**********&x-signature=6O2ZC5u3JirsFp5Ztmjrkg6qyXU%3D&from=327834062&s=PackSourceEnum_AWEME_DETAIL&se=false&sc=origin_cover&biz_tag=pcweb_cover&l=20250611224111BE76B20E6959870E308C', 'media_picurl': 'https://p3-pc.douyinpic.com/aweme/100x100/aweme-avatar/douyin-user-image-file_b63d5b8cff159c78f6a5b5a141e1bf78.jpeg?from=327834062', 'music_url': 'https://sf5-hl-cdn-tos.douyinstatic.com/obj/ies-music/7512407324665858867.mp3', 'music_author_name': '快乐小狗兔🐰', 'music_id': '7512407325395733285', 'music_name': '@快乐小狗兔🐰创作的原声', 'row_id': 'UPDgkSikV4'}]
2025-06-11 22:41:24.467 INFO work_detail_processor.py __webhook_adapter Line:99 10140 - webhook params :[{'rowId': 'UPDgkSikV4', 'workUrl': 'https://www.douyin.com/video/7512407127186050315', 'likeCount': 7033, 'commentCount': 239, 'shareCount': 433, 'collectCount': 2010}]
2025-06-11 22:41:24.467 INFO xhs_detail_updater.py sync_to_dingtalk Line:283 10140 - 开始同步 1 条数据到钉钉
2025-06-11 22:41:28.408 INFO xhs_detail_updater.py sync_to_dingtalk Line:299 10140 - 成功同步数据到钉钉
2025-06-11 22:41:28.408 INFO xhs_detail_updater.py sync_to_dingtalk Line:300 10140 - 钉钉响应: {"data":true,"success":true}
2025-06-11 22:45:06.666 INFO work_detail_processor.py start_job Line:28 25444 - start company :佳尔优优
2025-06-11 22:45:06.666 INFO work_detail_processor.py start_job Line:31 25444 - 公司：佳尔优优, 配置没有开启: ['task-update-article-001']
2025-06-11 22:45:06.666 INFO work_detail_processor.py start_job Line:28 25444 - start company :朱栈科技
2025-06-11 22:45:06.666 INFO service_client.py __init__ Line:52 25444 - ServiceClient initialized with environment: production, base_url: https://api.jinsuosuo.com/core
2025-06-11 22:45:06.666 INFO xhs_detail_updater.py __init__ Line:49 25444 - XhsDetailUpdater 初始化完成
2025-06-11 22:45:06.667 INFO xhs_detail_updater.py run Line:61 25444 - ---- 开始执行详情更新流程 ---
2025-06-11 22:45:06.667 INFO xhs_detail_updater.py _query_tasks Line:102 25444 - 查询可更新任务， 平台: dy
2025-06-11 22:45:06.667 INFO service_client.py query_can_update Line:79 25444 - 调用queryCanUpdate接口: URL=https://api.jinsuosuo.com/core/api/external/queryCanUpdate, params={'platform': 'dy'}
2025-06-11 22:45:08.475 INFO service_client.py query_can_update Line:98 25444 - queryCanUpdate接口调用成功: {'code': 'success', 'result': [], 'msg': '操作成功'}
2025-06-11 22:45:08.476 INFO xhs_detail_updater.py _query_tasks Line:122 25444 - 没有待更新的任务
2025-06-11 22:45:10.503 INFO xhs_detail_updater.py run Line:68 25444 - 没有找到待更新的任务
2025-06-11 22:45:10.503 INFO work_detail_processor.py __process Line:55 25444 - {'api-token': 'api-token-71726a26c98b6a63a9bc9cac43ecab13', 'user_id': 121}, 没有需要更新的数据
2025-06-11 23:02:49.444 INFO indexer_work_detail_refresh.py main Line:23 21808 - 启动小红书数据更新定时任务
2025-06-11 23:02:49.444 INFO indexer_work_detail_refresh.py main Line:28 21808 - 当前时间: 2025-06-11 23:02:49
2025-06-11 23:02:49.444 INFO indexer_work_detail_refresh.py run_processor Line:14 21808 - 开始执行小红书数据更新任务...
2025-06-11 23:02:49.444 INFO work_detail_processor.py start_job Line:28 21808 - start company :佳尔优优
2025-06-11 23:02:49.446 INFO work_detail_processor.py start_job Line:31 21808 - 公司：佳尔优优, 配置没有开启: ['task-update-article-001']
2025-06-11 23:02:49.446 INFO work_detail_processor.py start_job Line:28 21808 - start company :朱栈科技
2025-06-11 23:02:49.446 ERROR indexer_work_detail_refresh.py run_processor Line:19 21808 - 执行任务时发生错误: name 'logger_' is not defined
2025-06-11 23:03:36.879 ERROR indexer_work_detail_refresh.py main Line:36 21808 - 程序被手动终止
2025-06-11 23:04:06.511 INFO indexer_work_detail_refresh.py main Line:23 21624 - 启动小红书数据更新定时任务
2025-06-11 23:04:06.511 INFO indexer_work_detail_refresh.py main Line:28 21624 - 当前时间: 2025-06-11 23:04:06
2025-06-11 23:04:15.908 INFO indexer_work_detail_refresh.py run_processor Line:14 21624 - 开始执行小红书数据更新任务...
2025-06-11 23:04:24.754 INFO work_detail_processor.py start_job Line:28 21624 - start company :佳尔优优
2025-06-11 23:04:24.754 INFO work_detail_processor.py start_job Line:31 21624 - 公司：佳尔优优, 配置没有开启: ['task-update-article-001']
2025-06-11 23:04:24.754 INFO work_detail_processor.py start_job Line:28 21624 - start company :朱栈科技
2025-06-11 23:04:52.801 ERROR indexer_work_detail_refresh.py main Line:36 21624 - 程序被手动终止
2025-06-11 23:05:13.895 INFO indexer_work_detail_refresh.py main Line:23 17772 - 启动小红书数据更新定时任务
2025-06-11 23:05:13.895 INFO indexer_work_detail_refresh.py main Line:28 17772 - 当前时间: 2025-06-11 23:05:13
2025-06-11 23:05:16.079 INFO indexer_work_detail_refresh.py run_processor Line:14 17772 - 开始执行小红书数据更新任务...
2025-06-11 23:05:22.487 ERROR indexer_work_detail_refresh.py main Line:36 17772 - 程序被手动终止
2025-06-11 23:05:31.896 INFO indexer_work_detail_refresh.py main Line:23 4116 - 启动小红书数据更新定时任务
2025-06-11 23:05:31.897 INFO indexer_work_detail_refresh.py main Line:28 4116 - 当前时间: 2025-06-11 23:05:31
2025-06-11 23:05:33.626 INFO indexer_work_detail_refresh.py run_processor Line:14 4116 - 开始执行小红书数据更新任务...
2025-06-11 23:05:34.676 INFO work_detail_processor.py start_job Line:28 4116 - start company :佳尔优优
2025-06-11 23:05:34.676 INFO work_detail_processor.py start_job Line:31 4116 - 公司：佳尔优优, 配置没有开启: ['task-update-article-001']
2025-06-11 23:05:34.676 INFO work_detail_processor.py start_job Line:28 4116 - start company :朱栈科技
2025-06-11 23:05:47.181 ERROR indexer_work_detail_refresh.py main Line:36 4116 - 程序被手动终止
2025-06-11 23:06:11.279 INFO indexer_work_detail_refresh.py main Line:23 11064 - 启动小红书数据更新定时任务
2025-06-11 23:06:11.279 INFO indexer_work_detail_refresh.py main Line:28 11064 - 当前时间: 2025-06-11 23:06:11
2025-06-11 23:06:13.993 INFO indexer_work_detail_refresh.py run_processor Line:14 11064 - 开始执行小红书数据更新任务...
2025-06-11 23:06:15.290 INFO work_detail_processor.py start_job Line:28 11064 - start company :佳尔优优
2025-06-11 23:06:15.290 INFO work_detail_processor.py start_job Line:31 11064 - 公司：佳尔优优, 配置没有开启: ['task-update-article-001']
2025-06-11 23:06:15.290 INFO work_detail_processor.py start_job Line:28 11064 - start company :朱栈科技
2025-06-11 23:07:10.738 ERROR indexer_work_detail_refresh.py main Line:36 11064 - 程序被手动终止
2025-06-11 23:07:14.479 INFO indexer_work_detail_refresh.py main Line:30 22704 - 启动小红书数据更新定时任务
2025-06-11 23:07:14.480 INFO indexer_work_detail_refresh.py main Line:35 22704 - 当前时间: 2025-06-11 23:07:14
2025-06-11 23:07:16.670 INFO indexer_work_detail_refresh.py run_processor Line:21 22704 - 开始执行小红书数据更新任务...
2025-06-11 23:07:19.990 INFO work_detail_processor.py start_job Line:28 22704 - start company :佳尔优优
2025-06-11 23:07:19.990 INFO work_detail_processor.py start_job Line:31 22704 - 公司：佳尔优优, 配置没有开启: ['task-update-article-001']
2025-06-11 23:07:19.990 INFO work_detail_processor.py start_job Line:28 22704 - start company :朱栈科技
2025-06-11 23:07:27.072 ERROR indexer_work_detail_refresh.py main Line:43 22704 - 程序被手动终止
2025-06-11 23:09:36.809 INFO indexer_work_detail_refresh.py main Line:25 24276 - 启动小红书数据更新定时任务
2025-06-11 23:09:36.810 INFO indexer_work_detail_refresh.py main Line:30 24276 - 当前时间: 2025-06-11 23:09:36
2025-06-11 23:09:40.239 INFO indexer_work_detail_refresh.py run_processor Line:16 24276 - 开始执行小红书数据更新任务...
2025-06-11 23:09:47.785 INFO indexer_work_detail_refresh.py main Line:38 24276 - 程序被手动终止
2025-06-11 23:10:16.781 INFO indexer_work_detail_refresh.py main Line:25 12680 - 启动小红书数据更新定时任务
2025-06-11 23:10:16.781 INFO indexer_work_detail_refresh.py main Line:30 12680 - 当前时间: 2025-06-11 23:10:16
2025-06-11 23:10:18.860 INFO indexer_work_detail_refresh.py run_processor Line:16 12680 - 开始执行小红书数据更新任务...
2025-06-11 23:10:19.792 INFO work_detail_processor.py start_job Line:26 12680 - start company :佳尔优优
2025-06-11 23:10:19.792 INFO work_detail_processor.py start_job Line:29 12680 - 公司：佳尔优优, 配置没有开启: ['task-update-article-001']
2025-06-11 23:10:19.793 INFO work_detail_processor.py start_job Line:26 12680 - start company :朱栈科技
2025-06-11 23:10:21.758 ERROR indexer_work_detail_refresh.py run_processor Line:21 12680 - 执行任务时发生错误: name 'logger_' is not defined
2025-06-11 23:10:25.922 INFO indexer_work_detail_refresh.py main Line:38 12680 - 程序被手动终止
2025-06-11 23:12:25.148 INFO indexer_work_detail_refresh.py main Line:24 18532 - 启动小红书数据更新定时任务
2025-06-11 23:12:25.149 INFO indexer_work_detail_refresh.py main Line:29 18532 - 当前时间: 2025-06-11 23:12:25
2025-06-11 23:12:27.161 INFO indexer_work_detail_refresh.py run_processor Line:15 18532 - 开始执行小红书数据更新任务...
2025-06-11 23:12:30.060 INFO work_detail_processor.py start_job Line:26 18532 - start company :佳尔优优
2025-06-11 23:12:30.060 INFO work_detail_processor.py start_job Line:29 18532 - 公司：佳尔优优, 配置没有开启: ['task-update-article-001']
2025-06-11 23:12:30.060 INFO work_detail_processor.py start_job Line:26 18532 - start company :朱栈科技
2025-06-11 23:12:32.794 INFO indexer_work_detail_refresh.py main Line:37 18532 - 程序被手动终止
