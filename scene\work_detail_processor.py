# -*- coding: utf-8 -*-
# @Time : 2025/6/11 16:52
# <AUTHOR> cyf
# @File : work_detail_processor.py
from typing import Dict, List, Optional
from xmlrpc.client import Boolean

from client.qb.models import Platform
from client.service_client import Environment
from common.logger import Logger
from config.xhs_article_config import updater_dict, article_task_dict
from scene.xhs_detail_updater import XhsDetailUpdater


class WorkDetailProcessor:
    """
    小红书处理流程
    """

    def __init__(self, env: Environment, logger: Logger = None):
        self.logger_ = logger
        self.env = env

    def start_job(self):
        for _, v in updater_dict.items():
            self.logger_.info(f"start company :{_}")
            task_config = article_task_dict.get(v[0], {})
            if not self.__check_active(task_config):
                self.logger_.info(f"公司：{_}, 配置没有开启: {v}")
                continue
            webhook = task_config.get("bitable", {}).get("webhook")
            if not webhook:
                self.logger_.info(f"公司：{_}, 配置没有开启: {v}")
                continue
            platforms = task_config.get("platform", [])
            user_info = task_config.get("user_info", {})
            self.__process(platforms=platforms, user_info=user_info, webhook=webhook)

    def __check_active(self, task_config: Dict) -> Boolean:
        return task_config.get("is_active", False)

    def __process(self, platforms: List[str], user_info: Dict, webhook: str):
        for platform in platforms:
            enum_platform = Platform(platform)
            updater = self._generate_updater(
                platform=enum_platform, user_info=user_info, webhook=webhook
            )
            work_list = updater.run()
            if not work_list:
                self.logger_.info(f"{user_info}, 没有需要更新的数据")
                return
            self.logger_.info(f"获取到的作品列表为 : {work_list}")
            webhook_data = self.__webhook_adapter(work_list)
            updater.sync_to_dingtalk(webhook_data)

    def _generate_updater(
        self, platform: Platform, user_info: Dict, webhook: str
    ) -> XhsDetailUpdater:
        """
        "user_info": {
            "api-token": "api-token-xxx",
            "user_id": 9
        }
        @param platform:
        @param user_info:
        @return:
        """
        if not user_info or not user_info.get("api-token", ""):
            raise Exception("配置错误")
        api_token = user_info.get("api-token", "")
        updater: XhsDetailUpdater = XhsDetailUpdater(
            environment=self.env,
            logger_=self.logger_,
            platform=platform,
            api_token=api_token,
            dingtalk_webhook=webhook,
        )
        return updater

    def __webhook_adapter(self, work_list: List[Dict]) -> Optional[List[Dict]]:
        """
        @param work_list:
        @param platform:
        """
        if not work_list:
            return None

        webhook_params: List[Dict] = []
        for work in work_list:
            result = {
                "rowId": work.get("row_id", ""),
                "workUrl": work.get("news_url", ""),
                "likeCount": work.get("news_like_count", 0),
                "commentCount": work.get("news_comment_count", 0),
                "shareCount": work.get("news_reposts_count", 0),
                "collectCount": work.get("news_collect_cnt", 0),
            }
            webhook_params.append(result)
        self.logger_.info(f"webhook params :{webhook_params}")
        return webhook_params


if __name__ == "__main__":
    process = WorkDetailProcessor()
    process.start_job()
    pass
