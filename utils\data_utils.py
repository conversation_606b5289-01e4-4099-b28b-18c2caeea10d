# -*- encoding:utf-8 -*-

from common.enum.interval import Interval
from common.enum.matching_type import MatchingType


class DataUtils:
    """
    data 相关工具类
    """

    @staticmethod
    def exchange_interval(backtesting_mode):
        _mode_map = {
            '1d': Interval.DAILY,
            '1m': Interval.MINUTE,
            '1h': Interval.HOUR,
            'tick': Interval.TICK
        }

        return _mode_map.get(backtesting_mode)

    @staticmethod
    def exchange_matching_type(matching_type):
        matching_type_map = {
            'current_bar': MatchingType.CURRENT_BAR,
            'next_bar': MatchingType.NEXT_BAR
        }

        return matching_type_map.get(matching_type)
