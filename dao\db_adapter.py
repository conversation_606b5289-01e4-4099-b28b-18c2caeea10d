# -*- coding: utf-8 -*-

from common.env import Env
from config.db_conf import mysql_cfg
from dao.database.mysql_datasource import MysqlDataSource


class DBAdapter(object):
    """
    数据库适配
    """

    def __init__(self, logger):
        self._product_rdb_data_source = MysqlDataSource(mysql_cfg.get('product_db_config'), logger)

    def get_product_data_source(self):
        return self._product_rdb_data_source


g_database_adapter = DBAdapter(Env().get_main_logger())
g_database_product = g_database_adapter.get_product_data_source()

